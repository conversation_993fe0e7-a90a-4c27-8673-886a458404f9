import { query, transaction } from '../config/database';
import { logger } from '../utils/logger';
import { getWebSocketService } from './websocketService';

export class TaskSchedulerService {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly checkInterval = 60000; // Check every minute

  /**
   * Start the task scheduler service
   */
  public start(): void {
    if (this.intervalId) {
      logger.warn('Task scheduler is already running');
      return;
    }

    logger.info('Starting task scheduler service');
    
    // Run immediately on start
    this.checkExpiredTasks();
    
    // Then run every minute
    this.intervalId = setInterval(() => {
      this.checkExpiredTasks();
    }, this.checkInterval);

    logger.info(`Task scheduler started with ${this.checkInterval}ms interval`);
  }

  /**
   * Stop the task scheduler service
   */
  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Task scheduler service stopped');
    }
  }

  /**
   * Check for tasks that have passed their expected finish time and mark them as completed
   */
  private async checkExpiredTasks(): Promise<void> {
    try {
      logger.debug('Checking for expired tasks...');

      const result = await transaction(async (client) => {
        // Find tasks that have passed their expected finish time
        const expiredTasksResult = await client.query(`
          SELECT 
            tu.id,
            tu.user_id,
            tu.task_description,
            tu.status,
            tu.priority,
            tu.category,
            tu.estimated_duration_minutes,
            tu.progress_percentage,
            tu.project_id,
            tu.expected_completion_date,
            tu.blocking_issues,
            tu.expected_finish_datetime,
            tu.created_at,
            u.name as user_name
          FROM task_updates tu
          JOIN users u ON tu.user_id = u.id
          WHERE tu.expected_finish_datetime IS NOT NULL
            AND tu.expected_finish_datetime <= CURRENT_TIMESTAMP
            AND tu.status IN ('active', 'idle')
            AND u.is_active = true
        `);

        if (expiredTasksResult.rows.length === 0) {
          logger.debug('No expired tasks found');
          return { completedTasks: 0 };
        }

        logger.info(`Found ${expiredTasksResult.rows.length} expired tasks`);

        let completedCount = 0;

        for (const task of expiredTasksResult.rows) {
          try {
            // Calculate session duration (time from creation to expected finish)
            const sessionDurationMs = new Date(task.expected_finish_datetime).getTime() - new Date(task.created_at).getTime();
            const sessionDurationMinutes = Math.max(1, Math.round(sessionDurationMs / (1000 * 60)));

            // Move task to history with 'completed' action
            await client.query(`
              INSERT INTO task_history (
                user_id, task_update_id, task_description, status, priority, category,
                estimated_duration_minutes, progress_percentage, project_id,
                expected_completion_date, blocking_issues, number_of_questions,
                expected_finish_datetime, action_type, session_duration_minutes, created_at
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CURRENT_TIMESTAMP)
            `, [
              task.user_id,
              task.id,
              task.task_description,
              task.status,
              task.priority,
              task.category,
              task.estimated_duration_minutes,
              task.progress_percentage,
              task.project_id,
              task.expected_completion_date,
              task.blocking_issues,
              task.number_of_questions,
              task.expected_finish_datetime,
              'completed',
              sessionDurationMinutes
            ]);

            // Remove the task from task_updates (user now has no current task)
            await client.query('DELETE FROM task_updates WHERE id = $1', [task.id]);

            completedCount++;

            logger.info(`Auto-completed expired task for user ${task.user_name} (${task.user_id}): "${task.task_description}"`);

            // Broadcast status update - user is now 'idle' since task is completed
            try {
              const wsService = getWebSocketService();
              if (wsService) {
                wsService.broadcastStatusUpdate(task.user_id, 'idle');
              }
            } catch (wsError) {
              logger.error('Failed to broadcast status update for auto-completed task', {
                userId: task.user_id,
                error: wsError
              });
            }

          } catch (taskError) {
            logger.error(`Failed to complete expired task ${task.id}:`, taskError);
          }
        }

        return { completedTasks: completedCount };
      });

      if (result.completedTasks > 0) {
        logger.info(`Successfully auto-completed ${result.completedTasks} expired tasks`);
      }

    } catch (error) {
      logger.error('Error checking expired tasks:', error);
    }
  }

  /**
   * Manually complete a task (for API endpoint)
   */
  public async completeTask(userId: string, taskId: string): Promise<boolean> {
    try {
      const result = await transaction(async (client) => {
        // Get the current task
        const taskResult = await client.query(`
          SELECT * FROM task_updates 
          WHERE id = $1 AND user_id = $2
        `, [taskId, userId]);

        if (taskResult.rows.length === 0) {
          throw new Error('Task not found or access denied');
        }

        const task = taskResult.rows[0];

        // Calculate session duration
        const sessionDurationMs = Date.now() - new Date(task.created_at).getTime();
        const sessionDurationMinutes = Math.max(1, Math.round(sessionDurationMs / (1000 * 60)));

        // Move to history
        await client.query(`
          INSERT INTO task_history (
            user_id, task_update_id, task_description, status, priority, category,
            estimated_duration_minutes, progress_percentage, project_id,
            expected_completion_date, blocking_issues, number_of_questions,
            expected_finish_datetime, action_type, session_duration_minutes, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CURRENT_TIMESTAMP)
        `, [
          task.user_id,
          task.id,
          task.task_description,
          task.status,
          task.priority,
          task.category,
          task.estimated_duration_minutes,
          task.progress_percentage,
          task.project_id,
          task.expected_completion_date,
          task.blocking_issues,
          task.number_of_questions,
          task.expected_finish_datetime,
          'completed',
          sessionDurationMinutes
        ]);

        // Remove from current tasks
        await client.query('DELETE FROM task_updates WHERE id = $1', [taskId]);

        return true;
      });

      logger.info(`Manually completed task ${taskId} for user ${userId}`);

      // Broadcast status update - user is now 'idle' since task is completed
      if (result) {
        try {
          const wsService = getWebSocketService();
          if (wsService) {
            wsService.broadcastStatusUpdate(userId, 'idle');
          }
        } catch (wsError) {
          logger.error('Failed to broadcast status update for manually completed task', {
            userId,
            taskId,
            error: wsError
          });
        }
      }

      return result;

    } catch (error) {
      logger.error(`Failed to complete task ${taskId} for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Get tasks that are approaching their deadline (within next hour)
   */
  public async getTasksApproachingDeadline(): Promise<any[]> {
    try {
      const result = await query(`
        SELECT 
          tu.id,
          tu.user_id,
          tu.task_description,
          tu.expected_finish_datetime,
          u.name as user_name,
          u.email as user_email
        FROM task_updates tu
        JOIN users u ON tu.user_id = u.id
        WHERE tu.expected_finish_datetime IS NOT NULL
          AND tu.expected_finish_datetime > CURRENT_TIMESTAMP
          AND tu.expected_finish_datetime <= CURRENT_TIMESTAMP + INTERVAL '1 hour'
          AND tu.status IN ('active', 'idle')
          AND u.is_active = true
        ORDER BY tu.expected_finish_datetime ASC
      `);

      return result.rows;
    } catch (error) {
      logger.error('Error getting tasks approaching deadline:', error);
      return [];
    }
  }
}

// Export singleton instance
export const taskScheduler = new TaskSchedulerService();
