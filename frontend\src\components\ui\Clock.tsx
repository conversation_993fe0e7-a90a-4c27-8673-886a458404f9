import React, { useState, useEffect } from 'react';
import { cn } from '../../utils/cn';
import { getCurrentTime12Hour, getCurrentTime12HourWithSeconds } from '../../utils/time';

export interface ClockProps extends React.HTMLAttributes<HTMLDivElement> {
  showSeconds?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'badge';
}

const Clock = React.forwardRef<HTMLDivElement, ClockProps>(
  ({ className, showSeconds = false, size = 'md', variant = 'default', ...props }, ref) => {
    const [currentTime, setCurrentTime] = useState<string>('');

    useEffect(() => {
      const updateTime = () => {
        const time = showSeconds ? getCurrentTime12HourWithSeconds() : getCurrentTime12Hour();
        setCurrentTime(time);
      };

      // Update immediately
      updateTime();

      // Set up interval to update every second if showing seconds, otherwise every 30 seconds
      const interval = setInterval(updateTime, showSeconds ? 1000 : 30000);

      return () => clearInterval(interval);
    }, [showSeconds]);

    const sizeClasses = {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg'
    };

    const variantClasses = {
      default: 'text-neutral-700 font-medium',
      minimal: 'text-neutral-600 font-normal',
      badge: 'text-neutral-700 font-semibold'
    };

    return (
      <div
        ref={ref}
        className={cn(
          'clock inline-flex items-center',
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
        title={`Current time: ${currentTime}`}
        {...props}
      >
        <svg
          className="w-5 h-5 mr-2 text-neutral-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span className="tabular-nums">
          {currentTime || (showSeconds ? getCurrentTime12HourWithSeconds() : getCurrentTime12Hour())}
        </span>
      </div>
    );
  }
);

Clock.displayName = 'Clock';

export default Clock;
