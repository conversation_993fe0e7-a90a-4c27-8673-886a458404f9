-- Migration: Convert project from foreign key reference to simple text field
-- This allows users to enter any project/client name without database constraints

-- Add project_name column to task_updates table
ALTER TABLE task_updates 
ADD COLUMN project_name VARCHAR(255);

-- Add project_name column to task_history table for consistency
ALTER TABLE task_history 
ADD COLUMN project_name VA<PERSON>HAR(255);

-- Copy existing project names from projects table to task_updates
UPDATE task_updates 
SET project_name = p.name 
FROM projects p 
WHERE task_updates.project_id = p.id;

-- Copy existing project names from projects table to task_history
UPDATE task_history 
SET project_name = p.name 
FROM projects p 
WHERE task_history.project_id = p.id;

-- Note: We're keeping the project_id columns for now to maintain data integrity
-- In a production environment, you might want to:
-- 1. Backup the data
-- 2. Verify the migration worked correctly
-- 3. Then drop the project_id columns and foreign key constraints
-- 
-- For this implementation, we'll use project_name going forward
-- and ignore project_id in new records
