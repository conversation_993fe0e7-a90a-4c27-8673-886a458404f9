(function(){const h=document.createElement("link").relList;if(h&&h.supports&&h.supports("modulepreload"))return;for(const y of document.querySelectorAll('link[rel="modulepreload"]'))c(y);new MutationObserver(y=>{for(const z of y)if(z.type==="childList")for(const p of z.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&c(p)}).observe(document,{childList:!0,subtree:!0});function d(y){const z={};return y.integrity&&(z.integrity=y.integrity),y.referrerPolicy&&(z.referrerPolicy=y.referrerPolicy),y.crossOrigin==="use-credentials"?z.credentials="include":y.crossOrigin==="anonymous"?z.credentials="omit":z.credentials="same-origin",z}function c(y){if(y.ep)return;y.ep=!0;const z=d(y);fetch(y.href,z)}})();function Rh(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}var Nc={exports:{}},Gn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Of;function Hh(){if(Of)return Gn;Of=1;var o=Symbol.for("react.transitional.element"),h=Symbol.for("react.fragment");function d(c,y,z){var p=null;if(z!==void 0&&(p=""+z),y.key!==void 0&&(p=""+y.key),"key"in y){z={};for(var R in y)R!=="key"&&(z[R]=y[R])}else z=y;return y=z.ref,{$$typeof:o,type:c,key:p,ref:y!==void 0?y:null,props:z}}return Gn.Fragment=h,Gn.jsx=d,Gn.jsxs=d,Gn}var Df;function Lh(){return Df||(Df=1,Nc.exports=Hh()),Nc.exports}var s=Lh(),Sc={exports:{}},re={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uf;function Bh(){if(Uf)return re;Uf=1;var o=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),z=Symbol.for("react.consumer"),p=Symbol.for("react.context"),R=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),M=Symbol.iterator;function U(m){return m===null||typeof m!="object"?null:(m=M&&m[M]||m["@@iterator"],typeof m=="function"?m:null)}var O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},H=Object.assign,X={};function G(m,k,Q){this.props=m,this.context=k,this.refs=X,this.updater=Q||O}G.prototype.isReactComponent={},G.prototype.setState=function(m,k){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,k,"setState")},G.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function le(){}le.prototype=G.prototype;function F(m,k,Q){this.props=m,this.context=k,this.refs=X,this.updater=Q||O}var P=F.prototype=new le;P.constructor=F,H(P,G.prototype),P.isPureReactComponent=!0;var xe=Array.isArray,ee={H:null,A:null,T:null,S:null,V:null},ye=Object.prototype.hasOwnProperty;function ge(m,k,Q,B,K,fe){return Q=fe.ref,{$$typeof:o,type:m,key:k,ref:Q!==void 0?Q:null,props:fe}}function V(m,k){return ge(m.type,k,void 0,void 0,void 0,m.props)}function Ce(m){return typeof m=="object"&&m!==null&&m.$$typeof===o}function Be(m){var k={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(Q){return k[Q]})}var ie=/\/+/g;function Y(m,k){return typeof m=="object"&&m!==null&&m.key!=null?Be(""+m.key):k.toString(36)}function Ne(){}function ke(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(Ne,Ne):(m.status="pending",m.then(function(k){m.status==="pending"&&(m.status="fulfilled",m.value=k)},function(k){m.status==="pending"&&(m.status="rejected",m.reason=k)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function L(m,k,Q,B,K){var fe=typeof m;(fe==="undefined"||fe==="boolean")&&(m=null);var se=!1;if(m===null)se=!0;else switch(fe){case"bigint":case"string":case"number":se=!0;break;case"object":switch(m.$$typeof){case o:case h:se=!0;break;case S:return se=m._init,L(se(m._payload),k,Q,B,K)}}if(se)return K=K(m),se=B===""?"."+Y(m,0):B,xe(K)?(Q="",se!=null&&(Q=se.replace(ie,"$&/")+"/"),L(K,k,Q,"",function(ft){return ft})):K!=null&&(Ce(K)&&(K=V(K,Q+(K.key==null||m&&m.key===K.key?"":(""+K.key).replace(ie,"$&/")+"/")+se)),k.push(K)),1;se=0;var be=B===""?".":B+":";if(xe(m))for(var Ae=0;Ae<m.length;Ae++)B=m[Ae],fe=be+Y(B,Ae),se+=L(B,k,Q,fe,K);else if(Ae=U(m),typeof Ae=="function")for(m=Ae.call(m),Ae=0;!(B=m.next()).done;)B=B.value,fe=be+Y(B,Ae++),se+=L(B,k,Q,fe,K);else if(fe==="object"){if(typeof m.then=="function")return L(ke(m),k,Q,B,K);throw k=String(m),Error("Objects are not valid as a React child (found: "+(k==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":k)+"). If you meant to render a collection of children, use an array instead.")}return se}function N(m,k,Q){if(m==null)return m;var B=[],K=0;return L(m,B,"","",function(fe){return k.call(Q,fe,K++)}),B}function q(m){if(m._status===-1){var k=m._result;k=k(),k.then(function(Q){(m._status===0||m._status===-1)&&(m._status=1,m._result=Q)},function(Q){(m._status===0||m._status===-1)&&(m._status=2,m._result=Q)}),m._status===-1&&(m._status=0,m._result=k)}if(m._status===1)return m._result.default;throw m._result}var D=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var k=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(k))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function ce(){}return re.Children={map:N,forEach:function(m,k,Q){N(m,function(){k.apply(this,arguments)},Q)},count:function(m){var k=0;return N(m,function(){k++}),k},toArray:function(m){return N(m,function(k){return k})||[]},only:function(m){if(!Ce(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},re.Component=G,re.Fragment=d,re.Profiler=y,re.PureComponent=F,re.StrictMode=c,re.Suspense=T,re.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ee,re.__COMPILER_RUNTIME={__proto__:null,c:function(m){return ee.H.useMemoCache(m)}},re.cache=function(m){return function(){return m.apply(null,arguments)}},re.cloneElement=function(m,k,Q){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var B=H({},m.props),K=m.key,fe=void 0;if(k!=null)for(se in k.ref!==void 0&&(fe=void 0),k.key!==void 0&&(K=""+k.key),k)!ye.call(k,se)||se==="key"||se==="__self"||se==="__source"||se==="ref"&&k.ref===void 0||(B[se]=k[se]);var se=arguments.length-2;if(se===1)B.children=Q;else if(1<se){for(var be=Array(se),Ae=0;Ae<se;Ae++)be[Ae]=arguments[Ae+2];B.children=be}return ge(m.type,K,void 0,void 0,fe,B)},re.createContext=function(m){return m={$$typeof:p,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:z,_context:m},m},re.createElement=function(m,k,Q){var B,K={},fe=null;if(k!=null)for(B in k.key!==void 0&&(fe=""+k.key),k)ye.call(k,B)&&B!=="key"&&B!=="__self"&&B!=="__source"&&(K[B]=k[B]);var se=arguments.length-2;if(se===1)K.children=Q;else if(1<se){for(var be=Array(se),Ae=0;Ae<se;Ae++)be[Ae]=arguments[Ae+2];K.children=be}if(m&&m.defaultProps)for(B in se=m.defaultProps,se)K[B]===void 0&&(K[B]=se[B]);return ge(m,fe,void 0,void 0,null,K)},re.createRef=function(){return{current:null}},re.forwardRef=function(m){return{$$typeof:R,render:m}},re.isValidElement=Ce,re.lazy=function(m){return{$$typeof:S,_payload:{_status:-1,_result:m},_init:q}},re.memo=function(m,k){return{$$typeof:g,type:m,compare:k===void 0?null:k}},re.startTransition=function(m){var k=ee.T,Q={};ee.T=Q;try{var B=m(),K=ee.S;K!==null&&K(Q,B),typeof B=="object"&&B!==null&&typeof B.then=="function"&&B.then(ce,D)}catch(fe){D(fe)}finally{ee.T=k}},re.unstable_useCacheRefresh=function(){return ee.H.useCacheRefresh()},re.use=function(m){return ee.H.use(m)},re.useActionState=function(m,k,Q){return ee.H.useActionState(m,k,Q)},re.useCallback=function(m,k){return ee.H.useCallback(m,k)},re.useContext=function(m){return ee.H.useContext(m)},re.useDebugValue=function(){},re.useDeferredValue=function(m,k){return ee.H.useDeferredValue(m,k)},re.useEffect=function(m,k,Q){var B=ee.H;if(typeof Q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return B.useEffect(m,k)},re.useId=function(){return ee.H.useId()},re.useImperativeHandle=function(m,k,Q){return ee.H.useImperativeHandle(m,k,Q)},re.useInsertionEffect=function(m,k){return ee.H.useInsertionEffect(m,k)},re.useLayoutEffect=function(m,k){return ee.H.useLayoutEffect(m,k)},re.useMemo=function(m,k){return ee.H.useMemo(m,k)},re.useOptimistic=function(m,k){return ee.H.useOptimistic(m,k)},re.useReducer=function(m,k,Q){return ee.H.useReducer(m,k,Q)},re.useRef=function(m){return ee.H.useRef(m)},re.useState=function(m){return ee.H.useState(m)},re.useSyncExternalStore=function(m,k,Q){return ee.H.useSyncExternalStore(m,k,Q)},re.useTransition=function(){return ee.H.useTransition()},re.version="19.1.0",re}var Rf;function Uc(){return Rf||(Rf=1,Sc.exports=Bh()),Sc.exports}var Z=Uc();const dt=Rh(Z);var wc={exports:{}},Qn={},Tc={exports:{}},Ec={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hf;function qh(){return Hf||(Hf=1,function(o){function h(N,q){var D=N.length;N.push(q);e:for(;0<D;){var ce=D-1>>>1,m=N[ce];if(0<y(m,q))N[ce]=q,N[D]=m,D=ce;else break e}}function d(N){return N.length===0?null:N[0]}function c(N){if(N.length===0)return null;var q=N[0],D=N.pop();if(D!==q){N[0]=D;e:for(var ce=0,m=N.length,k=m>>>1;ce<k;){var Q=2*(ce+1)-1,B=N[Q],K=Q+1,fe=N[K];if(0>y(B,D))K<m&&0>y(fe,B)?(N[ce]=fe,N[K]=D,ce=K):(N[ce]=B,N[Q]=D,ce=Q);else if(K<m&&0>y(fe,D))N[ce]=fe,N[K]=D,ce=K;else break e}}return q}function y(N,q){var D=N.sortIndex-q.sortIndex;return D!==0?D:N.id-q.id}if(o.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var z=performance;o.unstable_now=function(){return z.now()}}else{var p=Date,R=p.now();o.unstable_now=function(){return p.now()-R}}var T=[],g=[],S=1,M=null,U=3,O=!1,H=!1,X=!1,G=!1,le=typeof setTimeout=="function"?setTimeout:null,F=typeof clearTimeout=="function"?clearTimeout:null,P=typeof setImmediate<"u"?setImmediate:null;function xe(N){for(var q=d(g);q!==null;){if(q.callback===null)c(g);else if(q.startTime<=N)c(g),q.sortIndex=q.expirationTime,h(T,q);else break;q=d(g)}}function ee(N){if(X=!1,xe(N),!H)if(d(T)!==null)H=!0,ye||(ye=!0,Y());else{var q=d(g);q!==null&&L(ee,q.startTime-N)}}var ye=!1,ge=-1,V=5,Ce=-1;function Be(){return G?!0:!(o.unstable_now()-Ce<V)}function ie(){if(G=!1,ye){var N=o.unstable_now();Ce=N;var q=!0;try{e:{H=!1,X&&(X=!1,F(ge),ge=-1),O=!0;var D=U;try{t:{for(xe(N),M=d(T);M!==null&&!(M.expirationTime>N&&Be());){var ce=M.callback;if(typeof ce=="function"){M.callback=null,U=M.priorityLevel;var m=ce(M.expirationTime<=N);if(N=o.unstable_now(),typeof m=="function"){M.callback=m,xe(N),q=!0;break t}M===d(T)&&c(T),xe(N)}else c(T);M=d(T)}if(M!==null)q=!0;else{var k=d(g);k!==null&&L(ee,k.startTime-N),q=!1}}break e}finally{M=null,U=D,O=!1}q=void 0}}finally{q?Y():ye=!1}}}var Y;if(typeof P=="function")Y=function(){P(ie)};else if(typeof MessageChannel<"u"){var Ne=new MessageChannel,ke=Ne.port2;Ne.port1.onmessage=ie,Y=function(){ke.postMessage(null)}}else Y=function(){le(ie,0)};function L(N,q){ge=le(function(){N(o.unstable_now())},q)}o.unstable_IdlePriority=5,o.unstable_ImmediatePriority=1,o.unstable_LowPriority=4,o.unstable_NormalPriority=3,o.unstable_Profiling=null,o.unstable_UserBlockingPriority=2,o.unstable_cancelCallback=function(N){N.callback=null},o.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<N?Math.floor(1e3/N):5},o.unstable_getCurrentPriorityLevel=function(){return U},o.unstable_next=function(N){switch(U){case 1:case 2:case 3:var q=3;break;default:q=U}var D=U;U=q;try{return N()}finally{U=D}},o.unstable_requestPaint=function(){G=!0},o.unstable_runWithPriority=function(N,q){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var D=U;U=N;try{return q()}finally{U=D}},o.unstable_scheduleCallback=function(N,q,D){var ce=o.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?ce+D:ce):D=ce,N){case 1:var m=-1;break;case 2:m=250;break;case 5:m=1073741823;break;case 4:m=1e4;break;default:m=5e3}return m=D+m,N={id:S++,callback:q,priorityLevel:N,startTime:D,expirationTime:m,sortIndex:-1},D>ce?(N.sortIndex=D,h(g,N),d(T)===null&&N===d(g)&&(X?(F(ge),ge=-1):X=!0,L(ee,D-ce))):(N.sortIndex=m,h(T,N),H||O||(H=!0,ye||(ye=!0,Y()))),N},o.unstable_shouldYield=Be,o.unstable_wrapCallback=function(N){var q=U;return function(){var D=U;U=q;try{return N.apply(this,arguments)}finally{U=D}}}}(Ec)),Ec}var Lf;function Yh(){return Lf||(Lf=1,Tc.exports=qh()),Tc.exports}var zc={exports:{}},at={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bf;function Gh(){if(Bf)return at;Bf=1;var o=Uc();function h(T){var g="https://react.dev/errors/"+T;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var S=2;S<arguments.length;S++)g+="&args[]="+encodeURIComponent(arguments[S])}return"Minified React error #"+T+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(){}var c={d:{f:d,r:function(){throw Error(h(522))},D:d,C:d,L:d,m:d,X:d,S:d,M:d},p:0,findDOMNode:null},y=Symbol.for("react.portal");function z(T,g,S){var M=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:y,key:M==null?null:""+M,children:T,containerInfo:g,implementation:S}}var p=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function R(T,g){if(T==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return at.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,at.createPortal=function(T,g){var S=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(h(299));return z(T,g,null,S)},at.flushSync=function(T){var g=p.T,S=c.p;try{if(p.T=null,c.p=2,T)return T()}finally{p.T=g,c.p=S,c.d.f()}},at.preconnect=function(T,g){typeof T=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,c.d.C(T,g))},at.prefetchDNS=function(T){typeof T=="string"&&c.d.D(T)},at.preinit=function(T,g){if(typeof T=="string"&&g&&typeof g.as=="string"){var S=g.as,M=R(S,g.crossOrigin),U=typeof g.integrity=="string"?g.integrity:void 0,O=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;S==="style"?c.d.S(T,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:M,integrity:U,fetchPriority:O}):S==="script"&&c.d.X(T,{crossOrigin:M,integrity:U,fetchPriority:O,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},at.preinitModule=function(T,g){if(typeof T=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var S=R(g.as,g.crossOrigin);c.d.M(T,{crossOrigin:S,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&c.d.M(T)},at.preload=function(T,g){if(typeof T=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var S=g.as,M=R(S,g.crossOrigin);c.d.L(T,S,{crossOrigin:M,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},at.preloadModule=function(T,g){if(typeof T=="string")if(g){var S=R(g.as,g.crossOrigin);c.d.m(T,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:S,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else c.d.m(T)},at.requestFormReset=function(T){c.d.r(T)},at.unstable_batchedUpdates=function(T,g){return T(g)},at.useFormState=function(T,g,S){return p.H.useFormState(T,g,S)},at.useFormStatus=function(){return p.H.useHostTransitionStatus()},at.version="19.1.0",at}var qf;function Qh(){if(qf)return zc.exports;qf=1;function o(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o)}catch(h){console.error(h)}}return o(),zc.exports=Gh(),zc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yf;function Xh(){if(Yf)return Qn;Yf=1;var o=Yh(),h=Uc(),d=Qh();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function y(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function z(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function p(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function R(e){if(z(e)!==e)throw Error(c(188))}function T(e){var t=e.alternate;if(!t){if(t=z(e),t===null)throw Error(c(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return R(n),e;if(i===a)return R(n),t;i=i.sibling}throw Error(c(188))}if(l.return!==a.return)l=n,a=i;else{for(var r=!1,u=n.child;u;){if(u===l){r=!0,l=n,a=i;break}if(u===a){r=!0,a=n,l=i;break}u=u.sibling}if(!r){for(u=i.child;u;){if(u===l){r=!0,l=i,a=n;break}if(u===a){r=!0,a=i,l=n;break}u=u.sibling}if(!r)throw Error(c(189))}}if(l.alternate!==a)throw Error(c(190))}if(l.tag!==3)throw Error(c(188));return l.stateNode.current===l?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var S=Object.assign,M=Symbol.for("react.element"),U=Symbol.for("react.transitional.element"),O=Symbol.for("react.portal"),H=Symbol.for("react.fragment"),X=Symbol.for("react.strict_mode"),G=Symbol.for("react.profiler"),le=Symbol.for("react.provider"),F=Symbol.for("react.consumer"),P=Symbol.for("react.context"),xe=Symbol.for("react.forward_ref"),ee=Symbol.for("react.suspense"),ye=Symbol.for("react.suspense_list"),ge=Symbol.for("react.memo"),V=Symbol.for("react.lazy"),Ce=Symbol.for("react.activity"),Be=Symbol.for("react.memo_cache_sentinel"),ie=Symbol.iterator;function Y(e){return e===null||typeof e!="object"?null:(e=ie&&e[ie]||e["@@iterator"],typeof e=="function"?e:null)}var Ne=Symbol.for("react.client.reference");function ke(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Ne?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case H:return"Fragment";case G:return"Profiler";case X:return"StrictMode";case ee:return"Suspense";case ye:return"SuspenseList";case Ce:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case O:return"Portal";case P:return(e.displayName||"Context")+".Provider";case F:return(e._context.displayName||"Context")+".Consumer";case xe:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ge:return t=e.displayName||null,t!==null?t:ke(e.type)||"Memo";case V:t=e._payload,e=e._init;try{return ke(e(t))}catch{}}return null}var L=Array.isArray,N=h.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=d.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},ce=[],m=-1;function k(e){return{current:e}}function Q(e){0>m||(e.current=ce[m],ce[m]=null,m--)}function B(e,t){m++,ce[m]=e.current,e.current=t}var K=k(null),fe=k(null),se=k(null),be=k(null);function Ae(e,t){switch(B(se,t),B(fe,e),B(K,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?rf(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=rf(t),e=cf(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Q(K),B(K,e)}function ft(){Q(K),Q(fe),Q(se)}function sl(e){e.memoizedState!==null&&B(be,e);var t=K.current,l=cf(t,e.type);t!==l&&(B(fe,e),B(K,l))}function il(e){fe.current===e&&(Q(K),Q(fe)),be.current===e&&(Q(be),Hn._currentValue=D)}var rl=Object.prototype.hasOwnProperty,ui=o.unstable_scheduleCallback,oi=o.unstable_cancelCallback,hm=o.unstable_shouldYield,xm=o.unstable_requestPaint,Rt=o.unstable_now,gm=o.unstable_getCurrentPriorityLevel,Bc=o.unstable_ImmediatePriority,qc=o.unstable_UserBlockingPriority,Zn=o.unstable_NormalPriority,pm=o.unstable_LowPriority,Yc=o.unstable_IdlePriority,vm=o.log,ym=o.unstable_setDisableYieldValue,Xa=null,mt=null;function cl(e){if(typeof vm=="function"&&ym(e),mt&&typeof mt.setStrictMode=="function")try{mt.setStrictMode(Xa,e)}catch{}}var ht=Math.clz32?Math.clz32:Nm,bm=Math.log,jm=Math.LN2;function Nm(e){return e>>>=0,e===0?32:31-(bm(e)/jm|0)|0}var Kn=256,Jn=4194304;function Ol(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function $n(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,i=e.suspendedLanes,r=e.pingedLanes;e=e.warmLanes;var u=a&134217727;return u!==0?(a=u&~i,a!==0?n=Ol(a):(r&=u,r!==0?n=Ol(r):l||(l=u&~e,l!==0&&(n=Ol(l))))):(u=a&~i,u!==0?n=Ol(u):r!==0?n=Ol(r):l||(l=a&~e,l!==0&&(n=Ol(l)))),n===0?0:t!==0&&t!==n&&(t&i)===0&&(i=n&-n,l=t&-t,i>=l||i===32&&(l&4194048)!==0)?t:n}function Va(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Sm(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gc(){var e=Kn;return Kn<<=1,(Kn&4194048)===0&&(Kn=256),e}function Qc(){var e=Jn;return Jn<<=1,(Jn&62914560)===0&&(Jn=4194304),e}function di(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function Za(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function wm(e,t,l,a,n,i){var r=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var u=e.entanglements,f=e.expirationTimes,j=e.hiddenUpdates;for(l=r&~l;0<l;){var _=31-ht(l),C=1<<_;u[_]=0,f[_]=-1;var w=j[_];if(w!==null)for(j[_]=null,_=0;_<w.length;_++){var E=w[_];E!==null&&(E.lane&=-536870913)}l&=~C}a!==0&&Xc(e,a,0),i!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=i&~(r&~t))}function Xc(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-ht(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function Vc(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-ht(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function fi(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function mi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Zc(){var e=q.p;return e!==0?e:(e=window.event,e===void 0?32:zf(e.type))}function Tm(e,t){var l=q.p;try{return q.p=e,t()}finally{q.p=l}}var ul=Math.random().toString(36).slice(2),tt="__reactFiber$"+ul,st="__reactProps$"+ul,ea="__reactContainer$"+ul,hi="__reactEvents$"+ul,Em="__reactListeners$"+ul,zm="__reactHandles$"+ul,Kc="__reactResources$"+ul,Ka="__reactMarker$"+ul;function xi(e){delete e[tt],delete e[st],delete e[hi],delete e[Em],delete e[zm]}function ta(e){var t=e[tt];if(t)return t;for(var l=e.parentNode;l;){if(t=l[ea]||l[tt]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=ff(e);e!==null;){if(l=e[tt])return l;e=ff(e)}return t}e=l,l=e.parentNode}return null}function la(e){if(e=e[tt]||e[ea]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ja(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function aa(e){var t=e[Kc];return t||(t=e[Kc]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ke(e){e[Ka]=!0}var Jc=new Set,$c={};function Dl(e,t){na(e,t),na(e+"Capture",t)}function na(e,t){for($c[e]=t,e=0;e<t.length;e++)Jc.add(t[e])}var _m=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Wc={},Fc={};function Am(e){return rl.call(Fc,e)?!0:rl.call(Wc,e)?!1:_m.test(e)?Fc[e]=!0:(Wc[e]=!0,!1)}function Wn(e,t,l){if(Am(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function Fn(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Gt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var gi,Ic;function sa(e){if(gi===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);gi=t&&t[1]||"",Ic=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+gi+e+Ic}var pi=!1;function vi(e,t){if(!e||pi)return"";pi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var C=function(){throw Error()};if(Object.defineProperty(C.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(C,[])}catch(E){var w=E}Reflect.construct(e,[],C)}else{try{C.call()}catch(E){w=E}e.call(C.prototype)}}else{try{throw Error()}catch(E){w=E}(C=e())&&typeof C.catch=="function"&&C.catch(function(){})}}catch(E){if(E&&w&&typeof E.stack=="string")return[E.stack,w.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),r=i[0],u=i[1];if(r&&u){var f=r.split(`
`),j=u.split(`
`);for(n=a=0;a<f.length&&!f[a].includes("DetermineComponentFrameRoot");)a++;for(;n<j.length&&!j[n].includes("DetermineComponentFrameRoot");)n++;if(a===f.length||n===j.length)for(a=f.length-1,n=j.length-1;1<=a&&0<=n&&f[a]!==j[n];)n--;for(;1<=a&&0<=n;a--,n--)if(f[a]!==j[n]){if(a!==1||n!==1)do if(a--,n--,0>n||f[a]!==j[n]){var _=`
`+f[a].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=a&&0<=n);break}}}finally{pi=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?sa(l):""}function Mm(e){switch(e.tag){case 26:case 27:case 5:return sa(e.type);case 16:return sa("Lazy");case 13:return sa("Suspense");case 19:return sa("SuspenseList");case 0:case 15:return vi(e.type,!1);case 11:return vi(e.type.render,!1);case 1:return vi(e.type,!0);case 31:return sa("Activity");default:return""}}function Pc(e){try{var t="";do t+=Mm(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function Nt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function eu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Cm(e){var t=eu(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(r){a=""+r,i.call(this,r)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(r){a=""+r},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function In(e){e._valueTracker||(e._valueTracker=Cm(e))}function tu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=eu(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function Pn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var km=/[\n"\\]/g;function St(e){return e.replace(km,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function yi(e,t,l,a,n,i,r,u){e.name="",r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?e.type=r:e.removeAttribute("type"),t!=null?r==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Nt(t)):e.value!==""+Nt(t)&&(e.value=""+Nt(t)):r!=="submit"&&r!=="reset"||e.removeAttribute("value"),t!=null?bi(e,r,Nt(t)):l!=null?bi(e,r,Nt(l)):a!=null&&e.removeAttribute("value"),n==null&&i!=null&&(e.defaultChecked=!!i),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"?e.name=""+Nt(u):e.removeAttribute("name")}function lu(e,t,l,a,n,i,r,u){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;l=l!=null?""+Nt(l):"",t=t!=null?""+Nt(t):l,u||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=u?e.checked:!!a,e.defaultChecked=!!a,r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.name=r)}function bi(e,t,l){t==="number"&&Pn(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function ia(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+Nt(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function au(e,t,l){if(t!=null&&(t=""+Nt(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+Nt(l):""}function nu(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(c(92));if(L(a)){if(1<a.length)throw Error(c(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=Nt(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function ra(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var Om=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function su(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||Om.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function iu(e,t,l){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&su(e,n,a)}else for(var i in t)t.hasOwnProperty(i)&&su(e,i,t[i])}function ji(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Dm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Um=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function es(e){return Um.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ni=null;function Si(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ca=null,ua=null;function ru(e){var t=la(e);if(t&&(e=t.stateNode)){var l=e[st]||null;e:switch(e=t.stateNode,t.type){case"input":if(yi(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+St(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[st]||null;if(!n)throw Error(c(90));yi(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&tu(a)}break e;case"textarea":au(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&ia(e,!!l.multiple,t,!1)}}}var wi=!1;function cu(e,t,l){if(wi)return e(t,l);wi=!0;try{var a=e(t);return a}finally{if(wi=!1,(ca!==null||ua!==null)&&(Bs(),ca&&(t=ca,e=ua,ua=ca=null,ru(t),e)))for(t=0;t<e.length;t++)ru(e[t])}}function $a(e,t){var l=e.stateNode;if(l===null)return null;var a=l[st]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(c(231,t,typeof l));return l}var Qt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ti=!1;if(Qt)try{var Wa={};Object.defineProperty(Wa,"passive",{get:function(){Ti=!0}}),window.addEventListener("test",Wa,Wa),window.removeEventListener("test",Wa,Wa)}catch{Ti=!1}var ol=null,Ei=null,ts=null;function uu(){if(ts)return ts;var e,t=Ei,l=t.length,a,n="value"in ol?ol.value:ol.textContent,i=n.length;for(e=0;e<l&&t[e]===n[e];e++);var r=l-e;for(a=1;a<=r&&t[l-a]===n[i-a];a++);return ts=n.slice(e,1<a?1-a:void 0)}function ls(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function as(){return!0}function ou(){return!1}function it(e){function t(l,a,n,i,r){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=r,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(l=e[u],this[u]=l?l(i):i[u]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?as:ou,this.isPropagationStopped=ou,this}return S(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=as)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=as)},persist:function(){},isPersistent:as}),t}var Ul={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ns=it(Ul),Fa=S({},Ul,{view:0,detail:0}),Rm=it(Fa),zi,_i,Ia,ss=S({},Fa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ia&&(Ia&&e.type==="mousemove"?(zi=e.screenX-Ia.screenX,_i=e.screenY-Ia.screenY):_i=zi=0,Ia=e),zi)},movementY:function(e){return"movementY"in e?e.movementY:_i}}),du=it(ss),Hm=S({},ss,{dataTransfer:0}),Lm=it(Hm),Bm=S({},Fa,{relatedTarget:0}),Ai=it(Bm),qm=S({},Ul,{animationName:0,elapsedTime:0,pseudoElement:0}),Ym=it(qm),Gm=S({},Ul,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qm=it(Gm),Xm=S({},Ul,{data:0}),fu=it(Xm),Vm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Km={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Jm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Km[e])?!!t[e]:!1}function Mi(){return Jm}var $m=S({},Fa,{key:function(e){if(e.key){var t=Vm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ls(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mi,charCode:function(e){return e.type==="keypress"?ls(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ls(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Wm=it($m),Fm=S({},ss,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),mu=it(Fm),Im=S({},Fa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mi}),Pm=it(Im),e0=S({},Ul,{propertyName:0,elapsedTime:0,pseudoElement:0}),t0=it(e0),l0=S({},ss,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),a0=it(l0),n0=S({},Ul,{newState:0,oldState:0}),s0=it(n0),i0=[9,13,27,32],Ci=Qt&&"CompositionEvent"in window,Pa=null;Qt&&"documentMode"in document&&(Pa=document.documentMode);var r0=Qt&&"TextEvent"in window&&!Pa,hu=Qt&&(!Ci||Pa&&8<Pa&&11>=Pa),xu=" ",gu=!1;function pu(e,t){switch(e){case"keyup":return i0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var oa=!1;function c0(e,t){switch(e){case"compositionend":return vu(t);case"keypress":return t.which!==32?null:(gu=!0,xu);case"textInput":return e=t.data,e===xu&&gu?null:e;default:return null}}function u0(e,t){if(oa)return e==="compositionend"||!Ci&&pu(e,t)?(e=uu(),ts=Ei=ol=null,oa=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return hu&&t.locale!=="ko"?null:t.data;default:return null}}var o0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!o0[e.type]:t==="textarea"}function bu(e,t,l,a){ca?ua?ua.push(a):ua=[a]:ca=a,t=Vs(t,"onChange"),0<t.length&&(l=new ns("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var en=null,tn=null;function d0(e){tf(e,0)}function is(e){var t=Ja(e);if(tu(t))return e}function ju(e,t){if(e==="change")return t}var Nu=!1;if(Qt){var ki;if(Qt){var Oi="oninput"in document;if(!Oi){var Su=document.createElement("div");Su.setAttribute("oninput","return;"),Oi=typeof Su.oninput=="function"}ki=Oi}else ki=!1;Nu=ki&&(!document.documentMode||9<document.documentMode)}function wu(){en&&(en.detachEvent("onpropertychange",Tu),tn=en=null)}function Tu(e){if(e.propertyName==="value"&&is(tn)){var t=[];bu(t,tn,e,Si(e)),cu(d0,t)}}function f0(e,t,l){e==="focusin"?(wu(),en=t,tn=l,en.attachEvent("onpropertychange",Tu)):e==="focusout"&&wu()}function m0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return is(tn)}function h0(e,t){if(e==="click")return is(t)}function x0(e,t){if(e==="input"||e==="change")return is(t)}function g0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:g0;function ln(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!rl.call(t,n)||!xt(e[n],t[n]))return!1}return!0}function Eu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function zu(e,t){var l=Eu(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Eu(l)}}function _u(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?_u(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Au(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Pn(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=Pn(e.document)}return t}function Di(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var p0=Qt&&"documentMode"in document&&11>=document.documentMode,da=null,Ui=null,an=null,Ri=!1;function Mu(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Ri||da==null||da!==Pn(a)||(a=da,"selectionStart"in a&&Di(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),an&&ln(an,a)||(an=a,a=Vs(Ui,"onSelect"),0<a.length&&(t=new ns("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=da)))}function Rl(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var fa={animationend:Rl("Animation","AnimationEnd"),animationiteration:Rl("Animation","AnimationIteration"),animationstart:Rl("Animation","AnimationStart"),transitionrun:Rl("Transition","TransitionRun"),transitionstart:Rl("Transition","TransitionStart"),transitioncancel:Rl("Transition","TransitionCancel"),transitionend:Rl("Transition","TransitionEnd")},Hi={},Cu={};Qt&&(Cu=document.createElement("div").style,"AnimationEvent"in window||(delete fa.animationend.animation,delete fa.animationiteration.animation,delete fa.animationstart.animation),"TransitionEvent"in window||delete fa.transitionend.transition);function Hl(e){if(Hi[e])return Hi[e];if(!fa[e])return e;var t=fa[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in Cu)return Hi[e]=t[l];return e}var ku=Hl("animationend"),Ou=Hl("animationiteration"),Du=Hl("animationstart"),v0=Hl("transitionrun"),y0=Hl("transitionstart"),b0=Hl("transitioncancel"),Uu=Hl("transitionend"),Ru=new Map,Li="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Li.push("scrollEnd");function Ct(e,t){Ru.set(e,t),Dl(t,[e])}var Hu=new WeakMap;function wt(e,t){if(typeof e=="object"&&e!==null){var l=Hu.get(e);return l!==void 0?l:(t={value:e,source:t,stack:Pc(t)},Hu.set(e,t),t)}return{value:e,source:t,stack:Pc(t)}}var Tt=[],ma=0,Bi=0;function rs(){for(var e=ma,t=Bi=ma=0;t<e;){var l=Tt[t];Tt[t++]=null;var a=Tt[t];Tt[t++]=null;var n=Tt[t];Tt[t++]=null;var i=Tt[t];if(Tt[t++]=null,a!==null&&n!==null){var r=a.pending;r===null?n.next=n:(n.next=r.next,r.next=n),a.pending=n}i!==0&&Lu(l,n,i)}}function cs(e,t,l,a){Tt[ma++]=e,Tt[ma++]=t,Tt[ma++]=l,Tt[ma++]=a,Bi|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function qi(e,t,l,a){return cs(e,t,l,a),us(e)}function ha(e,t){return cs(e,null,null,t),us(e)}function Lu(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=e.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(n=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,n&&t!==null&&(n=31-ht(l),e=i.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),i):null}function us(e){if(50<An)throw An=0,Zr=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var xa={};function j0(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function gt(e,t,l,a){return new j0(e,t,l,a)}function Yi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Xt(e,t){var l=e.alternate;return l===null?(l=gt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Bu(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function os(e,t,l,a,n,i){var r=0;if(a=e,typeof e=="function")Yi(e)&&(r=1);else if(typeof e=="string")r=Sh(e,l,K.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Ce:return e=gt(31,l,t,n),e.elementType=Ce,e.lanes=i,e;case H:return Ll(l.children,n,i,t);case X:r=8,n|=24;break;case G:return e=gt(12,l,t,n|2),e.elementType=G,e.lanes=i,e;case ee:return e=gt(13,l,t,n),e.elementType=ee,e.lanes=i,e;case ye:return e=gt(19,l,t,n),e.elementType=ye,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case le:case P:r=10;break e;case F:r=9;break e;case xe:r=11;break e;case ge:r=14;break e;case V:r=16,a=null;break e}r=29,l=Error(c(130,e===null?"null":typeof e,"")),a=null}return t=gt(r,l,t,n),t.elementType=e,t.type=a,t.lanes=i,t}function Ll(e,t,l,a){return e=gt(7,e,a,t),e.lanes=l,e}function Gi(e,t,l){return e=gt(6,e,null,t),e.lanes=l,e}function Qi(e,t,l){return t=gt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ga=[],pa=0,ds=null,fs=0,Et=[],zt=0,Bl=null,Vt=1,Zt="";function ql(e,t){ga[pa++]=fs,ga[pa++]=ds,ds=e,fs=t}function qu(e,t,l){Et[zt++]=Vt,Et[zt++]=Zt,Et[zt++]=Bl,Bl=e;var a=Vt;e=Zt;var n=32-ht(a)-1;a&=~(1<<n),l+=1;var i=32-ht(t)+n;if(30<i){var r=n-n%5;i=(a&(1<<r)-1).toString(32),a>>=r,n-=r,Vt=1<<32-ht(t)+n|l<<n|a,Zt=i+e}else Vt=1<<i|l<<n|a,Zt=e}function Xi(e){e.return!==null&&(ql(e,1),qu(e,1,0))}function Vi(e){for(;e===ds;)ds=ga[--pa],ga[pa]=null,fs=ga[--pa],ga[pa]=null;for(;e===Bl;)Bl=Et[--zt],Et[zt]=null,Zt=Et[--zt],Et[zt]=null,Vt=Et[--zt],Et[zt]=null}var nt=null,Re=null,Se=!1,Yl=null,Ht=!1,Zi=Error(c(519));function Gl(e){var t=Error(c(418,""));throw rn(wt(t,e)),Zi}function Yu(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[tt]=e,t[st]=a,l){case"dialog":he("cancel",t),he("close",t);break;case"iframe":case"object":case"embed":he("load",t);break;case"video":case"audio":for(l=0;l<Cn.length;l++)he(Cn[l],t);break;case"source":he("error",t);break;case"img":case"image":case"link":he("error",t),he("load",t);break;case"details":he("toggle",t);break;case"input":he("invalid",t),lu(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),In(t);break;case"select":he("invalid",t);break;case"textarea":he("invalid",t),nu(t,a.value,a.defaultValue,a.children),In(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||sf(t.textContent,l)?(a.popover!=null&&(he("beforetoggle",t),he("toggle",t)),a.onScroll!=null&&he("scroll",t),a.onScrollEnd!=null&&he("scrollend",t),a.onClick!=null&&(t.onclick=Zs),t=!0):t=!1,t||Gl(e)}function Gu(e){for(nt=e.return;nt;)switch(nt.tag){case 5:case 13:Ht=!1;return;case 27:case 3:Ht=!0;return;default:nt=nt.return}}function nn(e){if(e!==nt)return!1;if(!Se)return Gu(e),Se=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||cc(e.type,e.memoizedProps)),l=!l),l&&Re&&Gl(e),Gu(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){Re=Ot(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}Re=null}}else t===27?(t=Re,El(e.type)?(e=fc,fc=null,Re=e):Re=t):Re=nt?Ot(e.stateNode.nextSibling):null;return!0}function sn(){Re=nt=null,Se=!1}function Qu(){var e=Yl;return e!==null&&(ut===null?ut=e:ut.push.apply(ut,e),Yl=null),e}function rn(e){Yl===null?Yl=[e]:Yl.push(e)}var Ki=k(null),Ql=null,Kt=null;function dl(e,t,l){B(Ki,t._currentValue),t._currentValue=l}function Jt(e){e._currentValue=Ki.current,Q(Ki)}function Ji(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function $i(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var i=n.dependencies;if(i!==null){var r=n.child;i=i.firstContext;e:for(;i!==null;){var u=i;i=n;for(var f=0;f<t.length;f++)if(u.context===t[f]){i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),Ji(i.return,l,e),a||(r=null);break e}i=u.next}}else if(n.tag===18){if(r=n.return,r===null)throw Error(c(341));r.lanes|=l,i=r.alternate,i!==null&&(i.lanes|=l),Ji(r,l,e),r=null}else r=n.child;if(r!==null)r.return=n;else for(r=n;r!==null;){if(r===e){r=null;break}if(n=r.sibling,n!==null){n.return=r.return,r=n;break}r=r.return}n=r}}function cn(e,t,l,a){e=null;for(var n=t,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var r=n.alternate;if(r===null)throw Error(c(387));if(r=r.memoizedProps,r!==null){var u=n.type;xt(n.pendingProps.value,r.value)||(e!==null?e.push(u):e=[u])}}else if(n===be.current){if(r=n.alternate,r===null)throw Error(c(387));r.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Hn):e=[Hn])}n=n.return}e!==null&&$i(t,e,l,a),t.flags|=262144}function ms(e){for(e=e.firstContext;e!==null;){if(!xt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Xl(e){Ql=e,Kt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function lt(e){return Xu(Ql,e)}function hs(e,t){return Ql===null&&Xl(e),Xu(e,t)}function Xu(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Kt===null){if(e===null)throw Error(c(308));Kt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Kt=Kt.next=t;return l}var N0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},S0=o.unstable_scheduleCallback,w0=o.unstable_NormalPriority,Xe={$$typeof:P,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Wi(){return{controller:new N0,data:new Map,refCount:0}}function un(e){e.refCount--,e.refCount===0&&S0(w0,function(){e.controller.abort()})}var on=null,Fi=0,va=0,ya=null;function T0(e,t){if(on===null){var l=on=[];Fi=0,va=Pr(),ya={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Fi++,t.then(Vu,Vu),t}function Vu(){if(--Fi===0&&on!==null){ya!==null&&(ya.status="fulfilled");var e=on;on=null,va=0,ya=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function E0(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Zu=N.S;N.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&T0(e,t),Zu!==null&&Zu(e,t)};var Vl=k(null);function Ii(){var e=Vl.current;return e!==null?e:Oe.pooledCache}function xs(e,t){t===null?B(Vl,Vl.current):B(Vl,t.pool)}function Ku(){var e=Ii();return e===null?null:{parent:Xe._currentValue,pool:e}}var dn=Error(c(460)),Ju=Error(c(474)),gs=Error(c(542)),Pi={then:function(){}};function $u(e){return e=e.status,e==="fulfilled"||e==="rejected"}function ps(){}function Wu(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(ps,ps),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Iu(e),e;default:if(typeof t.status=="string")t.then(ps,ps);else{if(e=Oe,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Iu(e),e}throw fn=t,dn}}var fn=null;function Fu(){if(fn===null)throw Error(c(459));var e=fn;return fn=null,e}function Iu(e){if(e===dn||e===gs)throw Error(c(483))}var fl=!1;function er(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function tr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ml(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function hl(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(we&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=us(e),Lu(e,null,l),t}return cs(e,a,t,l),us(e)}function mn(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Vc(e,l)}}function lr(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var r={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=r:i=i.next=r,l=l.next}while(l!==null);i===null?n=i=t:i=i.next=t}else n=i=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var ar=!1;function hn(){if(ar){var e=ya;if(e!==null)throw e}}function xn(e,t,l,a){ar=!1;var n=e.updateQueue;fl=!1;var i=n.firstBaseUpdate,r=n.lastBaseUpdate,u=n.shared.pending;if(u!==null){n.shared.pending=null;var f=u,j=f.next;f.next=null,r===null?i=j:r.next=j,r=f;var _=e.alternate;_!==null&&(_=_.updateQueue,u=_.lastBaseUpdate,u!==r&&(u===null?_.firstBaseUpdate=j:u.next=j,_.lastBaseUpdate=f))}if(i!==null){var C=n.baseState;r=0,_=j=f=null,u=i;do{var w=u.lane&-536870913,E=w!==u.lane;if(E?(ve&w)===w:(a&w)===w){w!==0&&w===va&&(ar=!0),_!==null&&(_=_.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});e:{var ne=e,te=u;w=t;var _e=l;switch(te.tag){case 1:if(ne=te.payload,typeof ne=="function"){C=ne.call(_e,C,w);break e}C=ne;break e;case 3:ne.flags=ne.flags&-65537|128;case 0:if(ne=te.payload,w=typeof ne=="function"?ne.call(_e,C,w):ne,w==null)break e;C=S({},C,w);break e;case 2:fl=!0}}w=u.callback,w!==null&&(e.flags|=64,E&&(e.flags|=8192),E=n.callbacks,E===null?n.callbacks=[w]:E.push(w))}else E={lane:w,tag:u.tag,payload:u.payload,callback:u.callback,next:null},_===null?(j=_=E,f=C):_=_.next=E,r|=w;if(u=u.next,u===null){if(u=n.shared.pending,u===null)break;E=u,u=E.next,E.next=null,n.lastBaseUpdate=E,n.shared.pending=null}}while(!0);_===null&&(f=C),n.baseState=f,n.firstBaseUpdate=j,n.lastBaseUpdate=_,i===null&&(n.shared.lanes=0),Nl|=r,e.lanes=r,e.memoizedState=C}}function Pu(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function eo(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)Pu(l[e],t)}var ba=k(null),vs=k(0);function to(e,t){e=tl,B(vs,e),B(ba,t),tl=e|t.baseLanes}function nr(){B(vs,tl),B(ba,ba.current)}function sr(){tl=vs.current,Q(ba),Q(vs)}var xl=0,ue=null,Ee=null,Ge=null,ys=!1,ja=!1,Zl=!1,bs=0,gn=0,Na=null,z0=0;function qe(){throw Error(c(321))}function ir(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!xt(e[l],t[l]))return!1;return!0}function rr(e,t,l,a,n,i){return xl=i,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,N.H=e===null||e.memoizedState===null?Bo:qo,Zl=!1,i=l(a,n),Zl=!1,ja&&(i=ao(t,l,a,n)),lo(e),i}function lo(e){N.H=Es;var t=Ee!==null&&Ee.next!==null;if(xl=0,Ge=Ee=ue=null,ys=!1,gn=0,Na=null,t)throw Error(c(300));e===null||Je||(e=e.dependencies,e!==null&&ms(e)&&(Je=!0))}function ao(e,t,l,a){ue=e;var n=0;do{if(ja&&(Na=null),gn=0,ja=!1,25<=n)throw Error(c(301));if(n+=1,Ge=Ee=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}N.H=D0,i=t(l,a)}while(ja);return i}function _0(){var e=N.H,t=e.useState()[0];return t=typeof t.then=="function"?pn(t):t,e=e.useState()[0],(Ee!==null?Ee.memoizedState:null)!==e&&(ue.flags|=1024),t}function cr(){var e=bs!==0;return bs=0,e}function ur(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function or(e){if(ys){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ys=!1}xl=0,Ge=Ee=ue=null,ja=!1,gn=bs=0,Na=null}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?ue.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Qe(){if(Ee===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=Ee.next;var t=Ge===null?ue.memoizedState:Ge.next;if(t!==null)Ge=t,Ee=e;else{if(e===null)throw ue.alternate===null?Error(c(467)):Error(c(310));Ee=e,e={memoizedState:Ee.memoizedState,baseState:Ee.baseState,baseQueue:Ee.baseQueue,queue:Ee.queue,next:null},Ge===null?ue.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function dr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function pn(e){var t=gn;return gn+=1,Na===null&&(Na=[]),e=Wu(Na,e,t),t=ue,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,N.H=t===null||t.memoizedState===null?Bo:qo),e}function js(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return pn(e);if(e.$$typeof===P)return lt(e)}throw Error(c(438,String(e)))}function fr(e){var t=null,l=ue.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=ue.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=dr(),ue.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=Be;return t.index++,l}function $t(e,t){return typeof t=="function"?t(e):t}function Ns(e){var t=Qe();return mr(t,Ee,e)}function mr(e,t,l){var a=e.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=l;var n=e.baseQueue,i=a.pending;if(i!==null){if(n!==null){var r=n.next;n.next=i.next,i.next=r}t.baseQueue=n=i,a.pending=null}if(i=e.baseState,n===null)e.memoizedState=i;else{t=n.next;var u=r=null,f=null,j=t,_=!1;do{var C=j.lane&-536870913;if(C!==j.lane?(ve&C)===C:(xl&C)===C){var w=j.revertLane;if(w===0)f!==null&&(f=f.next={lane:0,revertLane:0,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null}),C===va&&(_=!0);else if((xl&w)===w){j=j.next,w===va&&(_=!0);continue}else C={lane:0,revertLane:j.revertLane,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null},f===null?(u=f=C,r=i):f=f.next=C,ue.lanes|=w,Nl|=w;C=j.action,Zl&&l(i,C),i=j.hasEagerState?j.eagerState:l(i,C)}else w={lane:C,revertLane:j.revertLane,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null},f===null?(u=f=w,r=i):f=f.next=w,ue.lanes|=C,Nl|=C;j=j.next}while(j!==null&&j!==t);if(f===null?r=i:f.next=u,!xt(i,e.memoizedState)&&(Je=!0,_&&(l=ya,l!==null)))throw l;e.memoizedState=i,e.baseState=r,e.baseQueue=f,a.lastRenderedState=i}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function hr(e){var t=Qe(),l=t.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,i=t.memoizedState;if(n!==null){l.pending=null;var r=n=n.next;do i=e(i,r.action),r=r.next;while(r!==n);xt(i,t.memoizedState)||(Je=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),l.lastRenderedState=i}return[i,a]}function no(e,t,l){var a=ue,n=Qe(),i=Se;if(i){if(l===void 0)throw Error(c(407));l=l()}else l=t();var r=!xt((Ee||n).memoizedState,l);r&&(n.memoizedState=l,Je=!0),n=n.queue;var u=ro.bind(null,a,n,e);if(vn(2048,8,u,[e]),n.getSnapshot!==t||r||Ge!==null&&Ge.memoizedState.tag&1){if(a.flags|=2048,Sa(9,Ss(),io.bind(null,a,n,l,t),null),Oe===null)throw Error(c(349));i||(xl&124)!==0||so(a,t,l)}return l}function so(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=ue.updateQueue,t===null?(t=dr(),ue.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function io(e,t,l,a){t.value=l,t.getSnapshot=a,co(t)&&uo(e)}function ro(e,t,l){return l(function(){co(t)&&uo(e)})}function co(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!xt(e,l)}catch{return!0}}function uo(e){var t=ha(e,2);t!==null&&jt(t,e,2)}function xr(e){var t=rt();if(typeof e=="function"){var l=e;if(e=l(),Zl){cl(!0);try{l()}finally{cl(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$t,lastRenderedState:e},t}function oo(e,t,l,a){return e.baseState=l,mr(e,Ee,typeof a=="function"?a:$t)}function A0(e,t,l,a,n){if(Ts(e))throw Error(c(485));if(e=t.action,e!==null){var i={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(r){i.listeners.push(r)}};N.T!==null?l(!0):i.isTransition=!1,a(i),l=t.pending,l===null?(i.next=t.pending=i,fo(t,i)):(i.next=l.next,t.pending=l.next=i)}}function fo(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var i=N.T,r={};N.T=r;try{var u=l(n,a),f=N.S;f!==null&&f(r,u),mo(e,t,u)}catch(j){gr(e,t,j)}finally{N.T=i}}else try{i=l(n,a),mo(e,t,i)}catch(j){gr(e,t,j)}}function mo(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){ho(e,t,a)},function(a){return gr(e,t,a)}):ho(e,t,l)}function ho(e,t,l){t.status="fulfilled",t.value=l,xo(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,fo(e,l)))}function gr(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,xo(t),t=t.next;while(t!==a)}e.action=null}function xo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function go(e,t){return t}function po(e,t){if(Se){var l=Oe.formState;if(l!==null){e:{var a=ue;if(Se){if(Re){t:{for(var n=Re,i=Ht;n.nodeType!==8;){if(!i){n=null;break t}if(n=Ot(n.nextSibling),n===null){n=null;break t}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Re=Ot(n.nextSibling),a=n.data==="F!";break e}}Gl(a)}a=!1}a&&(t=l[0])}}return l=rt(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:go,lastRenderedState:t},l.queue=a,l=Ro.bind(null,ue,a),a.dispatch=l,a=xr(!1),i=jr.bind(null,ue,!1,a.queue),a=rt(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=A0.bind(null,ue,n,i,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function vo(e){var t=Qe();return yo(t,Ee,e)}function yo(e,t,l){if(t=mr(e,t,go)[0],e=Ns($t)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=pn(t)}catch(r){throw r===dn?gs:r}else a=t;t=Qe();var n=t.queue,i=n.dispatch;return l!==t.memoizedState&&(ue.flags|=2048,Sa(9,Ss(),M0.bind(null,n,l),null)),[a,i,e]}function M0(e,t){e.action=t}function bo(e){var t=Qe(),l=Ee;if(l!==null)return yo(t,l,e);Qe(),t=t.memoizedState,l=Qe();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function Sa(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=ue.updateQueue,t===null&&(t=dr(),ue.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Ss(){return{destroy:void 0,resource:void 0}}function jo(){return Qe().memoizedState}function ws(e,t,l,a){var n=rt();a=a===void 0?null:a,ue.flags|=e,n.memoizedState=Sa(1|t,Ss(),l,a)}function vn(e,t,l,a){var n=Qe();a=a===void 0?null:a;var i=n.memoizedState.inst;Ee!==null&&a!==null&&ir(a,Ee.memoizedState.deps)?n.memoizedState=Sa(t,i,l,a):(ue.flags|=e,n.memoizedState=Sa(1|t,i,l,a))}function No(e,t){ws(8390656,8,e,t)}function So(e,t){vn(2048,8,e,t)}function wo(e,t){return vn(4,2,e,t)}function To(e,t){return vn(4,4,e,t)}function Eo(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function zo(e,t,l){l=l!=null?l.concat([e]):null,vn(4,4,Eo.bind(null,t,e),l)}function pr(){}function _o(e,t){var l=Qe();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&ir(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function Ao(e,t){var l=Qe();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&ir(t,a[1]))return a[0];if(a=e(),Zl){cl(!0);try{e()}finally{cl(!1)}}return l.memoizedState=[a,t],a}function vr(e,t,l){return l===void 0||(xl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=kd(),ue.lanes|=e,Nl|=e,l)}function Mo(e,t,l,a){return xt(l,t)?l:ba.current!==null?(e=vr(e,l,a),xt(e,t)||(Je=!0),e):(xl&42)===0?(Je=!0,e.memoizedState=l):(e=kd(),ue.lanes|=e,Nl|=e,t)}function Co(e,t,l,a,n){var i=q.p;q.p=i!==0&&8>i?i:8;var r=N.T,u={};N.T=u,jr(e,!1,t,l);try{var f=n(),j=N.S;if(j!==null&&j(u,f),f!==null&&typeof f=="object"&&typeof f.then=="function"){var _=E0(f,a);yn(e,t,_,bt(e))}else yn(e,t,a,bt(e))}catch(C){yn(e,t,{then:function(){},status:"rejected",reason:C},bt())}finally{q.p=i,N.T=r}}function C0(){}function yr(e,t,l,a){if(e.tag!==5)throw Error(c(476));var n=ko(e).queue;Co(e,n,t,D,l===null?C0:function(){return Oo(e),l(a)})}function ko(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$t,lastRenderedState:D},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$t,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Oo(e){var t=ko(e).next.queue;yn(e,t,{},bt())}function br(){return lt(Hn)}function Do(){return Qe().memoizedState}function Uo(){return Qe().memoizedState}function k0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=bt();e=ml(l);var a=hl(t,e,l);a!==null&&(jt(a,t,l),mn(a,t,l)),t={cache:Wi()},e.payload=t;return}t=t.return}}function O0(e,t,l){var a=bt();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Ts(e)?Ho(t,l):(l=qi(e,t,l,a),l!==null&&(jt(l,e,a),Lo(l,t,a)))}function Ro(e,t,l){var a=bt();yn(e,t,l,a)}function yn(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Ts(e))Ho(t,n);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var r=t.lastRenderedState,u=i(r,l);if(n.hasEagerState=!0,n.eagerState=u,xt(u,r))return cs(e,t,n,0),Oe===null&&rs(),!1}catch{}finally{}if(l=qi(e,t,n,a),l!==null)return jt(l,e,a),Lo(l,t,a),!0}return!1}function jr(e,t,l,a){if(a={lane:2,revertLane:Pr(),action:a,hasEagerState:!1,eagerState:null,next:null},Ts(e)){if(t)throw Error(c(479))}else t=qi(e,l,a,2),t!==null&&jt(t,e,2)}function Ts(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function Ho(e,t){ja=ys=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function Lo(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Vc(e,l)}}var Es={readContext:lt,use:js,useCallback:qe,useContext:qe,useEffect:qe,useImperativeHandle:qe,useLayoutEffect:qe,useInsertionEffect:qe,useMemo:qe,useReducer:qe,useRef:qe,useState:qe,useDebugValue:qe,useDeferredValue:qe,useTransition:qe,useSyncExternalStore:qe,useId:qe,useHostTransitionStatus:qe,useFormState:qe,useActionState:qe,useOptimistic:qe,useMemoCache:qe,useCacheRefresh:qe},Bo={readContext:lt,use:js,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:lt,useEffect:No,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,ws(4194308,4,Eo.bind(null,t,e),l)},useLayoutEffect:function(e,t){return ws(4194308,4,e,t)},useInsertionEffect:function(e,t){ws(4,2,e,t)},useMemo:function(e,t){var l=rt();t=t===void 0?null:t;var a=e();if(Zl){cl(!0);try{e()}finally{cl(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=rt();if(l!==void 0){var n=l(t);if(Zl){cl(!0);try{l(t)}finally{cl(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=O0.bind(null,ue,e),[a.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:function(e){e=xr(e);var t=e.queue,l=Ro.bind(null,ue,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:pr,useDeferredValue:function(e,t){var l=rt();return vr(l,e,t)},useTransition:function(){var e=xr(!1);return e=Co.bind(null,ue,e.queue,!0,!1),rt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=ue,n=rt();if(Se){if(l===void 0)throw Error(c(407));l=l()}else{if(l=t(),Oe===null)throw Error(c(349));(ve&124)!==0||so(a,t,l)}n.memoizedState=l;var i={value:l,getSnapshot:t};return n.queue=i,No(ro.bind(null,a,i,e),[e]),a.flags|=2048,Sa(9,Ss(),io.bind(null,a,i,l,t),null),l},useId:function(){var e=rt(),t=Oe.identifierPrefix;if(Se){var l=Zt,a=Vt;l=(a&~(1<<32-ht(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=bs++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=z0++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:br,useFormState:po,useActionState:po,useOptimistic:function(e){var t=rt();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=jr.bind(null,ue,!0,l),l.dispatch=t,[e,t]},useMemoCache:fr,useCacheRefresh:function(){return rt().memoizedState=k0.bind(null,ue)}},qo={readContext:lt,use:js,useCallback:_o,useContext:lt,useEffect:So,useImperativeHandle:zo,useInsertionEffect:wo,useLayoutEffect:To,useMemo:Ao,useReducer:Ns,useRef:jo,useState:function(){return Ns($t)},useDebugValue:pr,useDeferredValue:function(e,t){var l=Qe();return Mo(l,Ee.memoizedState,e,t)},useTransition:function(){var e=Ns($t)[0],t=Qe().memoizedState;return[typeof e=="boolean"?e:pn(e),t]},useSyncExternalStore:no,useId:Do,useHostTransitionStatus:br,useFormState:vo,useActionState:vo,useOptimistic:function(e,t){var l=Qe();return oo(l,Ee,e,t)},useMemoCache:fr,useCacheRefresh:Uo},D0={readContext:lt,use:js,useCallback:_o,useContext:lt,useEffect:So,useImperativeHandle:zo,useInsertionEffect:wo,useLayoutEffect:To,useMemo:Ao,useReducer:hr,useRef:jo,useState:function(){return hr($t)},useDebugValue:pr,useDeferredValue:function(e,t){var l=Qe();return Ee===null?vr(l,e,t):Mo(l,Ee.memoizedState,e,t)},useTransition:function(){var e=hr($t)[0],t=Qe().memoizedState;return[typeof e=="boolean"?e:pn(e),t]},useSyncExternalStore:no,useId:Do,useHostTransitionStatus:br,useFormState:bo,useActionState:bo,useOptimistic:function(e,t){var l=Qe();return Ee!==null?oo(l,Ee,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:fr,useCacheRefresh:Uo},wa=null,bn=0;function zs(e){var t=bn;return bn+=1,wa===null&&(wa=[]),Wu(wa,e,t)}function jn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function _s(e,t){throw t.$$typeof===M?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Yo(e){var t=e._init;return t(e._payload)}function Go(e){function t(v,x){if(e){var b=v.deletions;b===null?(v.deletions=[x],v.flags|=16):b.push(x)}}function l(v,x){if(!e)return null;for(;x!==null;)t(v,x),x=x.sibling;return null}function a(v){for(var x=new Map;v!==null;)v.key!==null?x.set(v.key,v):x.set(v.index,v),v=v.sibling;return x}function n(v,x){return v=Xt(v,x),v.index=0,v.sibling=null,v}function i(v,x,b){return v.index=b,e?(b=v.alternate,b!==null?(b=b.index,b<x?(v.flags|=67108866,x):b):(v.flags|=67108866,x)):(v.flags|=1048576,x)}function r(v){return e&&v.alternate===null&&(v.flags|=67108866),v}function u(v,x,b,A){return x===null||x.tag!==6?(x=Gi(b,v.mode,A),x.return=v,x):(x=n(x,b),x.return=v,x)}function f(v,x,b,A){var J=b.type;return J===H?_(v,x,b.props.children,A,b.key):x!==null&&(x.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===V&&Yo(J)===x.type)?(x=n(x,b.props),jn(x,b),x.return=v,x):(x=os(b.type,b.key,b.props,null,v.mode,A),jn(x,b),x.return=v,x)}function j(v,x,b,A){return x===null||x.tag!==4||x.stateNode.containerInfo!==b.containerInfo||x.stateNode.implementation!==b.implementation?(x=Qi(b,v.mode,A),x.return=v,x):(x=n(x,b.children||[]),x.return=v,x)}function _(v,x,b,A,J){return x===null||x.tag!==7?(x=Ll(b,v.mode,A,J),x.return=v,x):(x=n(x,b),x.return=v,x)}function C(v,x,b){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=Gi(""+x,v.mode,b),x.return=v,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case U:return b=os(x.type,x.key,x.props,null,v.mode,b),jn(b,x),b.return=v,b;case O:return x=Qi(x,v.mode,b),x.return=v,x;case V:var A=x._init;return x=A(x._payload),C(v,x,b)}if(L(x)||Y(x))return x=Ll(x,v.mode,b,null),x.return=v,x;if(typeof x.then=="function")return C(v,zs(x),b);if(x.$$typeof===P)return C(v,hs(v,x),b);_s(v,x)}return null}function w(v,x,b,A){var J=x!==null?x.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return J!==null?null:u(v,x,""+b,A);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case U:return b.key===J?f(v,x,b,A):null;case O:return b.key===J?j(v,x,b,A):null;case V:return J=b._init,b=J(b._payload),w(v,x,b,A)}if(L(b)||Y(b))return J!==null?null:_(v,x,b,A,null);if(typeof b.then=="function")return w(v,x,zs(b),A);if(b.$$typeof===P)return w(v,x,hs(v,b),A);_s(v,b)}return null}function E(v,x,b,A,J){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return v=v.get(b)||null,u(x,v,""+A,J);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case U:return v=v.get(A.key===null?b:A.key)||null,f(x,v,A,J);case O:return v=v.get(A.key===null?b:A.key)||null,j(x,v,A,J);case V:var de=A._init;return A=de(A._payload),E(v,x,b,A,J)}if(L(A)||Y(A))return v=v.get(b)||null,_(x,v,A,J,null);if(typeof A.then=="function")return E(v,x,b,zs(A),J);if(A.$$typeof===P)return E(v,x,b,hs(x,A),J);_s(x,A)}return null}function ne(v,x,b,A){for(var J=null,de=null,I=x,ae=x=0,We=null;I!==null&&ae<b.length;ae++){I.index>ae?(We=I,I=null):We=I.sibling;var je=w(v,I,b[ae],A);if(je===null){I===null&&(I=We);break}e&&I&&je.alternate===null&&t(v,I),x=i(je,x,ae),de===null?J=je:de.sibling=je,de=je,I=We}if(ae===b.length)return l(v,I),Se&&ql(v,ae),J;if(I===null){for(;ae<b.length;ae++)I=C(v,b[ae],A),I!==null&&(x=i(I,x,ae),de===null?J=I:de.sibling=I,de=I);return Se&&ql(v,ae),J}for(I=a(I);ae<b.length;ae++)We=E(I,v,ae,b[ae],A),We!==null&&(e&&We.alternate!==null&&I.delete(We.key===null?ae:We.key),x=i(We,x,ae),de===null?J=We:de.sibling=We,de=We);return e&&I.forEach(function(Cl){return t(v,Cl)}),Se&&ql(v,ae),J}function te(v,x,b,A){if(b==null)throw Error(c(151));for(var J=null,de=null,I=x,ae=x=0,We=null,je=b.next();I!==null&&!je.done;ae++,je=b.next()){I.index>ae?(We=I,I=null):We=I.sibling;var Cl=w(v,I,je.value,A);if(Cl===null){I===null&&(I=We);break}e&&I&&Cl.alternate===null&&t(v,I),x=i(Cl,x,ae),de===null?J=Cl:de.sibling=Cl,de=Cl,I=We}if(je.done)return l(v,I),Se&&ql(v,ae),J;if(I===null){for(;!je.done;ae++,je=b.next())je=C(v,je.value,A),je!==null&&(x=i(je,x,ae),de===null?J=je:de.sibling=je,de=je);return Se&&ql(v,ae),J}for(I=a(I);!je.done;ae++,je=b.next())je=E(I,v,ae,je.value,A),je!==null&&(e&&je.alternate!==null&&I.delete(je.key===null?ae:je.key),x=i(je,x,ae),de===null?J=je:de.sibling=je,de=je);return e&&I.forEach(function(Uh){return t(v,Uh)}),Se&&ql(v,ae),J}function _e(v,x,b,A){if(typeof b=="object"&&b!==null&&b.type===H&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case U:e:{for(var J=b.key;x!==null;){if(x.key===J){if(J=b.type,J===H){if(x.tag===7){l(v,x.sibling),A=n(x,b.props.children),A.return=v,v=A;break e}}else if(x.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===V&&Yo(J)===x.type){l(v,x.sibling),A=n(x,b.props),jn(A,b),A.return=v,v=A;break e}l(v,x);break}else t(v,x);x=x.sibling}b.type===H?(A=Ll(b.props.children,v.mode,A,b.key),A.return=v,v=A):(A=os(b.type,b.key,b.props,null,v.mode,A),jn(A,b),A.return=v,v=A)}return r(v);case O:e:{for(J=b.key;x!==null;){if(x.key===J)if(x.tag===4&&x.stateNode.containerInfo===b.containerInfo&&x.stateNode.implementation===b.implementation){l(v,x.sibling),A=n(x,b.children||[]),A.return=v,v=A;break e}else{l(v,x);break}else t(v,x);x=x.sibling}A=Qi(b,v.mode,A),A.return=v,v=A}return r(v);case V:return J=b._init,b=J(b._payload),_e(v,x,b,A)}if(L(b))return ne(v,x,b,A);if(Y(b)){if(J=Y(b),typeof J!="function")throw Error(c(150));return b=J.call(b),te(v,x,b,A)}if(typeof b.then=="function")return _e(v,x,zs(b),A);if(b.$$typeof===P)return _e(v,x,hs(v,b),A);_s(v,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,x!==null&&x.tag===6?(l(v,x.sibling),A=n(x,b),A.return=v,v=A):(l(v,x),A=Gi(b,v.mode,A),A.return=v,v=A),r(v)):l(v,x)}return function(v,x,b,A){try{bn=0;var J=_e(v,x,b,A);return wa=null,J}catch(I){if(I===dn||I===gs)throw I;var de=gt(29,I,null,v.mode);return de.lanes=A,de.return=v,de}finally{}}}var Ta=Go(!0),Qo=Go(!1),_t=k(null),Lt=null;function gl(e){var t=e.alternate;B(Ve,Ve.current&1),B(_t,e),Lt===null&&(t===null||ba.current!==null||t.memoizedState!==null)&&(Lt=e)}function Xo(e){if(e.tag===22){if(B(Ve,Ve.current),B(_t,e),Lt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Lt=e)}}else pl()}function pl(){B(Ve,Ve.current),B(_t,_t.current)}function Wt(e){Q(_t),Lt===e&&(Lt=null),Q(Ve)}var Ve=k(0);function As(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||dc(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Nr(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:S({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var Sr={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=bt(),n=ml(a);n.payload=t,l!=null&&(n.callback=l),t=hl(e,n,a),t!==null&&(jt(t,e,a),mn(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=bt(),n=ml(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=hl(e,n,a),t!==null&&(jt(t,e,a),mn(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=bt(),a=ml(l);a.tag=2,t!=null&&(a.callback=t),t=hl(e,a,l),t!==null&&(jt(t,e,l),mn(t,e,l))}};function Vo(e,t,l,a,n,i,r){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,i,r):t.prototype&&t.prototype.isPureReactComponent?!ln(l,a)||!ln(n,i):!0}function Zo(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&Sr.enqueueReplaceState(t,t.state,null)}function Kl(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=S({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var Ms=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Ko(e){Ms(e)}function Jo(e){console.error(e)}function $o(e){Ms(e)}function Cs(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Wo(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function wr(e,t,l){return l=ml(l),l.tag=3,l.payload={element:null},l.callback=function(){Cs(e,t)},l}function Fo(e){return e=ml(e),e.tag=3,e}function Io(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;e.payload=function(){return n(i)},e.callback=function(){Wo(t,l,a)}}var r=l.stateNode;r!==null&&typeof r.componentDidCatch=="function"&&(e.callback=function(){Wo(t,l,a),typeof n!="function"&&(Sl===null?Sl=new Set([this]):Sl.add(this));var u=a.stack;this.componentDidCatch(a.value,{componentStack:u!==null?u:""})})}function U0(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&cn(t,l,n,!0),l=_t.current,l!==null){switch(l.tag){case 13:return Lt===null?Jr():l.alternate===null&&He===0&&(He=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Pi?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Wr(e,a,n)),!1;case 22:return l.flags|=65536,a===Pi?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Wr(e,a,n)),!1}throw Error(c(435,l.tag))}return Wr(e,a,n),Jr(),!1}if(Se)return t=_t.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==Zi&&(e=Error(c(422),{cause:a}),rn(wt(e,l)))):(a!==Zi&&(t=Error(c(423),{cause:a}),rn(wt(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=wt(a,l),n=wr(e.stateNode,a,n),lr(e,n),He!==4&&(He=2)),!1;var i=Error(c(520),{cause:a});if(i=wt(i,l),_n===null?_n=[i]:_n.push(i),He!==4&&(He=2),t===null)return!0;a=wt(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=wr(l.stateNode,a,e),lr(l,e),!1;case 1:if(t=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(Sl===null||!Sl.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=Fo(n),Io(n,e,l,a),lr(l,n),!1}l=l.return}while(l!==null);return!1}var Po=Error(c(461)),Je=!1;function Fe(e,t,l,a){t.child=e===null?Qo(t,null,l,a):Ta(t,e.child,l,a)}function ed(e,t,l,a,n){l=l.render;var i=t.ref;if("ref"in a){var r={};for(var u in a)u!=="ref"&&(r[u]=a[u])}else r=a;return Xl(t),a=rr(e,t,l,r,i,n),u=cr(),e!==null&&!Je?(ur(e,t,n),Ft(e,t,n)):(Se&&u&&Xi(t),t.flags|=1,Fe(e,t,a,n),t.child)}function td(e,t,l,a,n){if(e===null){var i=l.type;return typeof i=="function"&&!Yi(i)&&i.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=i,ld(e,t,i,a,n)):(e=os(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!kr(e,n)){var r=i.memoizedProps;if(l=l.compare,l=l!==null?l:ln,l(r,a)&&e.ref===t.ref)return Ft(e,t,n)}return t.flags|=1,e=Xt(i,a),e.ref=t.ref,e.return=t,t.child=e}function ld(e,t,l,a,n){if(e!==null){var i=e.memoizedProps;if(ln(i,a)&&e.ref===t.ref)if(Je=!1,t.pendingProps=a=i,kr(e,n))(e.flags&131072)!==0&&(Je=!0);else return t.lanes=e.lanes,Ft(e,t,n)}return Tr(e,t,l,a,n)}function ad(e,t,l){var a=t.pendingProps,n=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,e!==null){for(n=t.child=e.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;t.childLanes=i&~a}else t.childLanes=0,t.child=null;return nd(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&xs(t,i!==null?i.cachePool:null),i!==null?to(t,i):nr(),Xo(t);else return t.lanes=t.childLanes=536870912,nd(e,t,i!==null?i.baseLanes|l:l,l)}else i!==null?(xs(t,i.cachePool),to(t,i),pl(),t.memoizedState=null):(e!==null&&xs(t,null),nr(),pl());return Fe(e,t,n,l),t.child}function nd(e,t,l,a){var n=Ii();return n=n===null?null:{parent:Xe._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&xs(t,null),nr(),Xo(t),e!==null&&cn(e,t,a,!0),null}function ks(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(c(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function Tr(e,t,l,a,n){return Xl(t),l=rr(e,t,l,a,void 0,n),a=cr(),e!==null&&!Je?(ur(e,t,n),Ft(e,t,n)):(Se&&a&&Xi(t),t.flags|=1,Fe(e,t,l,n),t.child)}function sd(e,t,l,a,n,i){return Xl(t),t.updateQueue=null,l=ao(t,a,l,n),lo(e),a=cr(),e!==null&&!Je?(ur(e,t,i),Ft(e,t,i)):(Se&&a&&Xi(t),t.flags|=1,Fe(e,t,l,i),t.child)}function id(e,t,l,a,n){if(Xl(t),t.stateNode===null){var i=xa,r=l.contextType;typeof r=="object"&&r!==null&&(i=lt(r)),i=new l(a,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Sr,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=a,i.state=t.memoizedState,i.refs={},er(t),r=l.contextType,i.context=typeof r=="object"&&r!==null?lt(r):xa,i.state=t.memoizedState,r=l.getDerivedStateFromProps,typeof r=="function"&&(Nr(t,l,r,a),i.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(r=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),r!==i.state&&Sr.enqueueReplaceState(i,i.state,null),xn(t,a,i,n),hn(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){i=t.stateNode;var u=t.memoizedProps,f=Kl(l,u);i.props=f;var j=i.context,_=l.contextType;r=xa,typeof _=="object"&&_!==null&&(r=lt(_));var C=l.getDerivedStateFromProps;_=typeof C=="function"||typeof i.getSnapshotBeforeUpdate=="function",u=t.pendingProps!==u,_||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u||j!==r)&&Zo(t,i,a,r),fl=!1;var w=t.memoizedState;i.state=w,xn(t,a,i,n),hn(),j=t.memoizedState,u||w!==j||fl?(typeof C=="function"&&(Nr(t,l,C,a),j=t.memoizedState),(f=fl||Vo(t,l,f,a,w,j,r))?(_||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=j),i.props=a,i.state=j,i.context=r,a=f):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{i=t.stateNode,tr(e,t),r=t.memoizedProps,_=Kl(l,r),i.props=_,C=t.pendingProps,w=i.context,j=l.contextType,f=xa,typeof j=="object"&&j!==null&&(f=lt(j)),u=l.getDerivedStateFromProps,(j=typeof u=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(r!==C||w!==f)&&Zo(t,i,a,f),fl=!1,w=t.memoizedState,i.state=w,xn(t,a,i,n),hn();var E=t.memoizedState;r!==C||w!==E||fl||e!==null&&e.dependencies!==null&&ms(e.dependencies)?(typeof u=="function"&&(Nr(t,l,u,a),E=t.memoizedState),(_=fl||Vo(t,l,_,a,w,E,f)||e!==null&&e.dependencies!==null&&ms(e.dependencies))?(j||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,E,f),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,E,f)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||r===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=E),i.props=a,i.state=E,i.context=f,a=_):(typeof i.componentDidUpdate!="function"||r===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),a=!1)}return i=a,ks(e,t),a=(t.flags&128)!==0,i||a?(i=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&a?(t.child=Ta(t,e.child,null,n),t.child=Ta(t,null,l,n)):Fe(e,t,l,n),t.memoizedState=i.state,e=t.child):e=Ft(e,t,n),e}function rd(e,t,l,a){return sn(),t.flags|=256,Fe(e,t,l,a),t.child}var Er={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function zr(e){return{baseLanes:e,cachePool:Ku()}}function _r(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=At),e}function cd(e,t,l){var a=t.pendingProps,n=!1,i=(t.flags&128)!==0,r;if((r=i)||(r=e!==null&&e.memoizedState===null?!1:(Ve.current&2)!==0),r&&(n=!0,t.flags&=-129),r=(t.flags&32)!==0,t.flags&=-33,e===null){if(Se){if(n?gl(t):pl(),Se){var u=Re,f;if(f=u){e:{for(f=u,u=Ht;f.nodeType!==8;){if(!u){u=null;break e}if(f=Ot(f.nextSibling),f===null){u=null;break e}}u=f}u!==null?(t.memoizedState={dehydrated:u,treeContext:Bl!==null?{id:Vt,overflow:Zt}:null,retryLane:536870912,hydrationErrors:null},f=gt(18,null,null,0),f.stateNode=u,f.return=t,t.child=f,nt=t,Re=null,f=!0):f=!1}f||Gl(t)}if(u=t.memoizedState,u!==null&&(u=u.dehydrated,u!==null))return dc(u)?t.lanes=32:t.lanes=536870912,null;Wt(t)}return u=a.children,a=a.fallback,n?(pl(),n=t.mode,u=Os({mode:"hidden",children:u},n),a=Ll(a,n,l,null),u.return=t,a.return=t,u.sibling=a,t.child=u,n=t.child,n.memoizedState=zr(l),n.childLanes=_r(e,r,l),t.memoizedState=Er,a):(gl(t),Ar(t,u))}if(f=e.memoizedState,f!==null&&(u=f.dehydrated,u!==null)){if(i)t.flags&256?(gl(t),t.flags&=-257,t=Mr(e,t,l)):t.memoizedState!==null?(pl(),t.child=e.child,t.flags|=128,t=null):(pl(),n=a.fallback,u=t.mode,a=Os({mode:"visible",children:a.children},u),n=Ll(n,u,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,Ta(t,e.child,null,l),a=t.child,a.memoizedState=zr(l),a.childLanes=_r(e,r,l),t.memoizedState=Er,t=n);else if(gl(t),dc(u)){if(r=u.nextSibling&&u.nextSibling.dataset,r)var j=r.dgst;r=j,a=Error(c(419)),a.stack="",a.digest=r,rn({value:a,source:null,stack:null}),t=Mr(e,t,l)}else if(Je||cn(e,t,l,!1),r=(l&e.childLanes)!==0,Je||r){if(r=Oe,r!==null&&(a=l&-l,a=(a&42)!==0?1:fi(a),a=(a&(r.suspendedLanes|l))!==0?0:a,a!==0&&a!==f.retryLane))throw f.retryLane=a,ha(e,a),jt(r,e,a),Po;u.data==="$?"||Jr(),t=Mr(e,t,l)}else u.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=f.treeContext,Re=Ot(u.nextSibling),nt=t,Se=!0,Yl=null,Ht=!1,e!==null&&(Et[zt++]=Vt,Et[zt++]=Zt,Et[zt++]=Bl,Vt=e.id,Zt=e.overflow,Bl=t),t=Ar(t,a.children),t.flags|=4096);return t}return n?(pl(),n=a.fallback,u=t.mode,f=e.child,j=f.sibling,a=Xt(f,{mode:"hidden",children:a.children}),a.subtreeFlags=f.subtreeFlags&65011712,j!==null?n=Xt(j,n):(n=Ll(n,u,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,u=e.child.memoizedState,u===null?u=zr(l):(f=u.cachePool,f!==null?(j=Xe._currentValue,f=f.parent!==j?{parent:j,pool:j}:f):f=Ku(),u={baseLanes:u.baseLanes|l,cachePool:f}),n.memoizedState=u,n.childLanes=_r(e,r,l),t.memoizedState=Er,a):(gl(t),l=e.child,e=l.sibling,l=Xt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=l,t.memoizedState=null,l)}function Ar(e,t){return t=Os({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Os(e,t){return e=gt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Mr(e,t,l){return Ta(t,e.child,null,l),e=Ar(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ud(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Ji(e.return,t,l)}function Cr(e,t,l,a,n){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function od(e,t,l){var a=t.pendingProps,n=a.revealOrder,i=a.tail;if(Fe(e,t,a.children,l),a=Ve.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ud(e,l,t);else if(e.tag===19)ud(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(B(Ve,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&As(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),Cr(t,!1,n,l,i);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&As(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}Cr(t,!0,l,null,i);break;case"together":Cr(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ft(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),Nl|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(cn(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,l=Xt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Xt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function kr(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ms(e)))}function R0(e,t,l){switch(t.tag){case 3:Ae(t,t.stateNode.containerInfo),dl(t,Xe,e.memoizedState.cache),sn();break;case 27:case 5:sl(t);break;case 4:Ae(t,t.stateNode.containerInfo);break;case 10:dl(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(gl(t),t.flags|=128,null):(l&t.child.childLanes)!==0?cd(e,t,l):(gl(t),e=Ft(e,t,l),e!==null?e.sibling:null);gl(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(cn(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return od(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),B(Ve,Ve.current),a)break;return null;case 22:case 23:return t.lanes=0,ad(e,t,l);case 24:dl(t,Xe,e.memoizedState.cache)}return Ft(e,t,l)}function dd(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Je=!0;else{if(!kr(e,l)&&(t.flags&128)===0)return Je=!1,R0(e,t,l);Je=(e.flags&131072)!==0}else Je=!1,Se&&(t.flags&1048576)!==0&&qu(t,fs,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")Yi(a)?(e=Kl(a,e),t.tag=1,t=id(null,t,a,e,l)):(t.tag=0,t=Tr(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===xe){t.tag=11,t=ed(null,t,a,e,l);break e}else if(n===ge){t.tag=14,t=td(null,t,a,e,l);break e}}throw t=ke(a)||a,Error(c(306,t,""))}}return t;case 0:return Tr(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=Kl(a,t.pendingProps),id(e,t,a,n,l);case 3:e:{if(Ae(t,t.stateNode.containerInfo),e===null)throw Error(c(387));a=t.pendingProps;var i=t.memoizedState;n=i.element,tr(e,t),xn(t,a,null,l);var r=t.memoizedState;if(a=r.cache,dl(t,Xe,a),a!==i.cache&&$i(t,[Xe],l,!0),hn(),a=r.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:r.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=rd(e,t,a,l);break e}else if(a!==n){n=wt(Error(c(424)),t),rn(n),t=rd(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Re=Ot(e.firstChild),nt=t,Se=!0,Yl=null,Ht=!0,l=Qo(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(sn(),a===n){t=Ft(e,t,l);break e}Fe(e,t,a,l)}t=t.child}return t;case 26:return ks(e,t),e===null?(l=gf(t.type,null,t.pendingProps,null))?t.memoizedState=l:Se||(l=t.type,e=t.pendingProps,a=Ks(se.current).createElement(l),a[tt]=t,a[st]=e,Pe(a,l,e),Ke(a),t.stateNode=a):t.memoizedState=gf(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return sl(t),e===null&&Se&&(a=t.stateNode=mf(t.type,t.pendingProps,se.current),nt=t,Ht=!0,n=Re,El(t.type)?(fc=n,Re=Ot(a.firstChild)):Re=n),Fe(e,t,t.pendingProps.children,l),ks(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Se&&((n=a=Re)&&(a=oh(a,t.type,t.pendingProps,Ht),a!==null?(t.stateNode=a,nt=t,Re=Ot(a.firstChild),Ht=!1,n=!0):n=!1),n||Gl(t)),sl(t),n=t.type,i=t.pendingProps,r=e!==null?e.memoizedProps:null,a=i.children,cc(n,i)?a=null:r!==null&&cc(n,r)&&(t.flags|=32),t.memoizedState!==null&&(n=rr(e,t,_0,null,null,l),Hn._currentValue=n),ks(e,t),Fe(e,t,a,l),t.child;case 6:return e===null&&Se&&((e=l=Re)&&(l=dh(l,t.pendingProps,Ht),l!==null?(t.stateNode=l,nt=t,Re=null,e=!0):e=!1),e||Gl(t)),null;case 13:return cd(e,t,l);case 4:return Ae(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Ta(t,null,a,l):Fe(e,t,a,l),t.child;case 11:return ed(e,t,t.type,t.pendingProps,l);case 7:return Fe(e,t,t.pendingProps,l),t.child;case 8:return Fe(e,t,t.pendingProps.children,l),t.child;case 12:return Fe(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,dl(t,t.type,a.value),Fe(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,Xl(t),n=lt(n),a=a(n),t.flags|=1,Fe(e,t,a,l),t.child;case 14:return td(e,t,t.type,t.pendingProps,l);case 15:return ld(e,t,t.type,t.pendingProps,l);case 19:return od(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=Os(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Xt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return ad(e,t,l);case 24:return Xl(t),a=lt(Xe),e===null?(n=Ii(),n===null&&(n=Oe,i=Wi(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),t.memoizedState={parent:a,cache:n},er(t),dl(t,Xe,n)):((e.lanes&l)!==0&&(tr(e,t),xn(t,null,null,l),hn()),n=e.memoizedState,i=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),dl(t,Xe,a)):(a=i.cache,dl(t,Xe,a),a!==n.cache&&$i(t,[Xe],l,!0))),Fe(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function It(e){e.flags|=4}function fd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!jf(t)){if(t=_t.current,t!==null&&((ve&4194048)===ve?Lt!==null:(ve&62914560)!==ve&&(ve&536870912)===0||t!==Lt))throw fn=Pi,Ju;e.flags|=8192}}function Ds(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Qc():536870912,e.lanes|=t,Aa|=t)}function Nn(e,t){if(!Se)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Ue(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function H0(e,t,l){var a=t.pendingProps;switch(Vi(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ue(t),null;case 1:return Ue(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Jt(Xe),ft(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(nn(t)?It(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Qu())),Ue(t),null;case 26:return l=t.memoizedState,e===null?(It(t),l!==null?(Ue(t),fd(t,l)):(Ue(t),t.flags&=-16777217)):l?l!==e.memoizedState?(It(t),Ue(t),fd(t,l)):(Ue(t),t.flags&=-16777217):(e.memoizedProps!==a&&It(t),Ue(t),t.flags&=-16777217),null;case 27:il(t),l=se.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&It(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Ue(t),null}e=K.current,nn(t)?Yu(t):(e=mf(n,a,l),t.stateNode=e,It(t))}return Ue(t),null;case 5:if(il(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&It(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Ue(t),null}if(e=K.current,nn(t))Yu(t);else{switch(n=Ks(se.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[tt]=t,e[st]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Pe(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&It(t)}}return Ue(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&It(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(c(166));if(e=se.current,nn(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=nt,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[tt]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||sf(e.nodeValue,l)),e||Gl(t)}else e=Ks(e).createTextNode(a),e[tt]=t,t.stateNode=e}return Ue(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=nn(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(c(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(c(317));n[tt]=t}else sn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ue(t),n=!1}else n=Qu(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Wt(t),t):(Wt(t),null)}if(Wt(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),Ds(t,t.updateQueue),Ue(t),null;case 4:return ft(),e===null&&ac(t.stateNode.containerInfo),Ue(t),null;case 10:return Jt(t.type),Ue(t),null;case 19:if(Q(Ve),n=t.memoizedState,n===null)return Ue(t),null;if(a=(t.flags&128)!==0,i=n.rendering,i===null)if(a)Nn(n,!1);else{if(He!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=As(e),i!==null){for(t.flags|=128,Nn(n,!1),e=i.updateQueue,t.updateQueue=e,Ds(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)Bu(l,e),l=l.sibling;return B(Ve,Ve.current&1|2),t.child}e=e.sibling}n.tail!==null&&Rt()>Hs&&(t.flags|=128,a=!0,Nn(n,!1),t.lanes=4194304)}else{if(!a)if(e=As(i),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Ds(t,e),Nn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!Se)return Ue(t),null}else 2*Rt()-n.renderingStartTime>Hs&&l!==536870912&&(t.flags|=128,a=!0,Nn(n,!1),t.lanes=4194304);n.isBackwards?(i.sibling=t.child,t.child=i):(e=n.last,e!==null?e.sibling=i:t.child=i,n.last=i)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Rt(),t.sibling=null,e=Ve.current,B(Ve,a?e&1|2:e&1),t):(Ue(t),null);case 22:case 23:return Wt(t),sr(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(Ue(t),t.subtreeFlags&6&&(t.flags|=8192)):Ue(t),l=t.updateQueue,l!==null&&Ds(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&Q(Vl),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Jt(Xe),Ue(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function L0(e,t){switch(Vi(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Jt(Xe),ft(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return il(t),null;case 13:if(Wt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));sn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Q(Ve),null;case 4:return ft(),null;case 10:return Jt(t.type),null;case 22:case 23:return Wt(t),sr(),e!==null&&Q(Vl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Jt(Xe),null;case 25:return null;default:return null}}function md(e,t){switch(Vi(t),t.tag){case 3:Jt(Xe),ft();break;case 26:case 27:case 5:il(t);break;case 4:ft();break;case 13:Wt(t);break;case 19:Q(Ve);break;case 10:Jt(t.type);break;case 22:case 23:Wt(t),sr(),e!==null&&Q(Vl);break;case 24:Jt(Xe)}}function Sn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var i=l.create,r=l.inst;a=i(),r.destroy=a}l=l.next}while(l!==n)}}catch(u){Me(t,t.return,u)}}function vl(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&e)===e){var r=a.inst,u=r.destroy;if(u!==void 0){r.destroy=void 0,n=t;var f=l,j=u;try{j()}catch(_){Me(n,f,_)}}}a=a.next}while(a!==i)}}catch(_){Me(t,t.return,_)}}function hd(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{eo(t,l)}catch(a){Me(e,e.return,a)}}}function xd(e,t,l){l.props=Kl(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){Me(e,t,a)}}function wn(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){Me(e,t,n)}}function Bt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Me(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Me(e,t,n)}else l.current=null}function gd(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Me(e,e.return,n)}}function Or(e,t,l){try{var a=e.stateNode;sh(a,e.type,l,t),a[st]=t}catch(n){Me(e,e.return,n)}}function pd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&El(e.type)||e.tag===4}function Dr(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||pd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&El(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ur(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=Zs));else if(a!==4&&(a===27&&El(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Ur(e,t,l),e=e.sibling;e!==null;)Ur(e,t,l),e=e.sibling}function Us(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&El(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(Us(e,t,l),e=e.sibling;e!==null;)Us(e,t,l),e=e.sibling}function vd(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Pe(t,a,l),t[tt]=e,t[st]=l}catch(i){Me(e,e.return,i)}}var Pt=!1,Ye=!1,Rr=!1,yd=typeof WeakSet=="function"?WeakSet:Set,$e=null;function B0(e,t){if(e=e.containerInfo,ic=Ps,e=Au(e),Di(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break e}var r=0,u=-1,f=-1,j=0,_=0,C=e,w=null;t:for(;;){for(var E;C!==l||n!==0&&C.nodeType!==3||(u=r+n),C!==i||a!==0&&C.nodeType!==3||(f=r+a),C.nodeType===3&&(r+=C.nodeValue.length),(E=C.firstChild)!==null;)w=C,C=E;for(;;){if(C===e)break t;if(w===l&&++j===n&&(u=r),w===i&&++_===a&&(f=r),(E=C.nextSibling)!==null)break;C=w,w=C.parentNode}C=E}l=u===-1||f===-1?null:{start:u,end:f}}else l=null}l=l||{start:0,end:0}}else l=null;for(rc={focusedElem:e,selectionRange:l},Ps=!1,$e=t;$e!==null;)if(t=$e,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,$e=e;else for(;$e!==null;){switch(t=$e,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,l=t,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var ne=Kl(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(ne,i),a.__reactInternalSnapshotBeforeUpdate=e}catch(te){Me(l,l.return,te)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)oc(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":oc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,$e=e;break}$e=t.return}}function bd(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:yl(e,l),a&4&&Sn(5,l);break;case 1:if(yl(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(r){Me(l,l.return,r)}else{var n=Kl(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(r){Me(l,l.return,r)}}a&64&&hd(l),a&512&&wn(l,l.return);break;case 3:if(yl(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{eo(e,t)}catch(r){Me(l,l.return,r)}}break;case 27:t===null&&a&4&&vd(l);case 26:case 5:yl(e,l),t===null&&a&4&&gd(l),a&512&&wn(l,l.return);break;case 12:yl(e,l);break;case 13:yl(e,l),a&4&&Sd(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=J0.bind(null,l),fh(e,l))));break;case 22:if(a=l.memoizedState!==null||Pt,!a){t=t!==null&&t.memoizedState!==null||Ye,n=Pt;var i=Ye;Pt=a,(Ye=t)&&!i?bl(e,l,(l.subtreeFlags&8772)!==0):yl(e,l),Pt=n,Ye=i}break;case 30:break;default:yl(e,l)}}function jd(e){var t=e.alternate;t!==null&&(e.alternate=null,jd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&xi(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var De=null,ct=!1;function el(e,t,l){for(l=l.child;l!==null;)Nd(e,t,l),l=l.sibling}function Nd(e,t,l){if(mt&&typeof mt.onCommitFiberUnmount=="function")try{mt.onCommitFiberUnmount(Xa,l)}catch{}switch(l.tag){case 26:Ye||Bt(l,t),el(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Ye||Bt(l,t);var a=De,n=ct;El(l.type)&&(De=l.stateNode,ct=!1),el(e,t,l),On(l.stateNode),De=a,ct=n;break;case 5:Ye||Bt(l,t);case 6:if(a=De,n=ct,De=null,el(e,t,l),De=a,ct=n,De!==null)if(ct)try{(De.nodeType===9?De.body:De.nodeName==="HTML"?De.ownerDocument.body:De).removeChild(l.stateNode)}catch(i){Me(l,t,i)}else try{De.removeChild(l.stateNode)}catch(i){Me(l,t,i)}break;case 18:De!==null&&(ct?(e=De,df(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),Yn(e)):df(De,l.stateNode));break;case 4:a=De,n=ct,De=l.stateNode.containerInfo,ct=!0,el(e,t,l),De=a,ct=n;break;case 0:case 11:case 14:case 15:Ye||vl(2,l,t),Ye||vl(4,l,t),el(e,t,l);break;case 1:Ye||(Bt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&xd(l,t,a)),el(e,t,l);break;case 21:el(e,t,l);break;case 22:Ye=(a=Ye)||l.memoizedState!==null,el(e,t,l),Ye=a;break;default:el(e,t,l)}}function Sd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Yn(e)}catch(l){Me(t,t.return,l)}}function q0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new yd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new yd),t;default:throw Error(c(435,e.tag))}}function Hr(e,t){var l=q0(e);t.forEach(function(a){var n=$0.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function pt(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=e,r=t,u=r;e:for(;u!==null;){switch(u.tag){case 27:if(El(u.type)){De=u.stateNode,ct=!1;break e}break;case 5:De=u.stateNode,ct=!1;break e;case 3:case 4:De=u.stateNode.containerInfo,ct=!0;break e}u=u.return}if(De===null)throw Error(c(160));Nd(i,r,n),De=null,ct=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)wd(t,e),t=t.sibling}var kt=null;function wd(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:pt(t,e),vt(e),a&4&&(vl(3,e,e.return),Sn(3,e),vl(5,e,e.return));break;case 1:pt(t,e),vt(e),a&512&&(Ye||l===null||Bt(l,l.return)),a&64&&Pt&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=kt;if(pt(t,e),vt(e),a&512&&(Ye||l===null||Bt(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[Ka]||i[tt]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),Pe(i,a,l),i[tt]=e,Ke(i),a=i;break e;case"link":var r=yf("link","href",n).get(a+(l.href||""));if(r){for(var u=0;u<r.length;u++)if(i=r[u],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){r.splice(u,1);break t}}i=n.createElement(a),Pe(i,a,l),n.head.appendChild(i);break;case"meta":if(r=yf("meta","content",n).get(a+(l.content||""))){for(u=0;u<r.length;u++)if(i=r[u],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){r.splice(u,1);break t}}i=n.createElement(a),Pe(i,a,l),n.head.appendChild(i);break;default:throw Error(c(468,a))}i[tt]=e,Ke(i),a=i}e.stateNode=a}else bf(n,e.type,e.stateNode);else e.stateNode=vf(n,a,e.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?bf(n,e.type,e.stateNode):vf(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Or(e,e.memoizedProps,l.memoizedProps)}break;case 27:pt(t,e),vt(e),a&512&&(Ye||l===null||Bt(l,l.return)),l!==null&&a&4&&Or(e,e.memoizedProps,l.memoizedProps);break;case 5:if(pt(t,e),vt(e),a&512&&(Ye||l===null||Bt(l,l.return)),e.flags&32){n=e.stateNode;try{ra(n,"")}catch(E){Me(e,e.return,E)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,Or(e,n,l!==null?l.memoizedProps:n)),a&1024&&(Rr=!0);break;case 6:if(pt(t,e),vt(e),a&4){if(e.stateNode===null)throw Error(c(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(E){Me(e,e.return,E)}}break;case 3:if(Ws=null,n=kt,kt=Js(t.containerInfo),pt(t,e),kt=n,vt(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Yn(t.containerInfo)}catch(E){Me(e,e.return,E)}Rr&&(Rr=!1,Td(e));break;case 4:a=kt,kt=Js(e.stateNode.containerInfo),pt(t,e),vt(e),kt=a;break;case 12:pt(t,e),vt(e);break;case 13:pt(t,e),vt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Qr=Rt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Hr(e,a)));break;case 22:n=e.memoizedState!==null;var f=l!==null&&l.memoizedState!==null,j=Pt,_=Ye;if(Pt=j||n,Ye=_||f,pt(t,e),Ye=_,Pt=j,vt(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||f||Pt||Ye||Jl(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){f=l=t;try{if(i=f.stateNode,n)r=i.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none";else{u=f.stateNode;var C=f.memoizedProps.style,w=C!=null&&C.hasOwnProperty("display")?C.display:null;u.style.display=w==null||typeof w=="boolean"?"":(""+w).trim()}}catch(E){Me(f,f.return,E)}}}else if(t.tag===6){if(l===null){f=t;try{f.stateNode.nodeValue=n?"":f.memoizedProps}catch(E){Me(f,f.return,E)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Hr(e,l))));break;case 19:pt(t,e),vt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Hr(e,a)));break;case 30:break;case 21:break;default:pt(t,e),vt(e)}}function vt(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(pd(a)){l=a;break}a=a.return}if(l==null)throw Error(c(160));switch(l.tag){case 27:var n=l.stateNode,i=Dr(e);Us(e,i,n);break;case 5:var r=l.stateNode;l.flags&32&&(ra(r,""),l.flags&=-33);var u=Dr(e);Us(e,u,r);break;case 3:case 4:var f=l.stateNode.containerInfo,j=Dr(e);Ur(e,j,f);break;default:throw Error(c(161))}}catch(_){Me(e,e.return,_)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Td(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Td(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function yl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)bd(e,t.alternate,t),t=t.sibling}function Jl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:vl(4,t,t.return),Jl(t);break;case 1:Bt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&xd(t,t.return,l),Jl(t);break;case 27:On(t.stateNode);case 26:case 5:Bt(t,t.return),Jl(t);break;case 22:t.memoizedState===null&&Jl(t);break;case 30:Jl(t);break;default:Jl(t)}e=e.sibling}}function bl(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,i=t,r=i.flags;switch(i.tag){case 0:case 11:case 15:bl(n,i,l),Sn(4,i);break;case 1:if(bl(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(j){Me(a,a.return,j)}if(a=i,n=a.updateQueue,n!==null){var u=a.stateNode;try{var f=n.shared.hiddenCallbacks;if(f!==null)for(n.shared.hiddenCallbacks=null,n=0;n<f.length;n++)Pu(f[n],u)}catch(j){Me(a,a.return,j)}}l&&r&64&&hd(i),wn(i,i.return);break;case 27:vd(i);case 26:case 5:bl(n,i,l),l&&a===null&&r&4&&gd(i),wn(i,i.return);break;case 12:bl(n,i,l);break;case 13:bl(n,i,l),l&&r&4&&Sd(n,i);break;case 22:i.memoizedState===null&&bl(n,i,l),wn(i,i.return);break;case 30:break;default:bl(n,i,l)}t=t.sibling}}function Lr(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&un(l))}function Br(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&un(e))}function qt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ed(e,t,l,a),t=t.sibling}function Ed(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:qt(e,t,l,a),n&2048&&Sn(9,t);break;case 1:qt(e,t,l,a);break;case 3:qt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&un(e)));break;case 12:if(n&2048){qt(e,t,l,a),e=t.stateNode;try{var i=t.memoizedProps,r=i.id,u=i.onPostCommit;typeof u=="function"&&u(r,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(f){Me(t,t.return,f)}}else qt(e,t,l,a);break;case 13:qt(e,t,l,a);break;case 23:break;case 22:i=t.stateNode,r=t.alternate,t.memoizedState!==null?i._visibility&2?qt(e,t,l,a):Tn(e,t):i._visibility&2?qt(e,t,l,a):(i._visibility|=2,Ea(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&Lr(r,t);break;case 24:qt(e,t,l,a),n&2048&&Br(t.alternate,t);break;default:qt(e,t,l,a)}}function Ea(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,r=t,u=l,f=a,j=r.flags;switch(r.tag){case 0:case 11:case 15:Ea(i,r,u,f,n),Sn(8,r);break;case 23:break;case 22:var _=r.stateNode;r.memoizedState!==null?_._visibility&2?Ea(i,r,u,f,n):Tn(i,r):(_._visibility|=2,Ea(i,r,u,f,n)),n&&j&2048&&Lr(r.alternate,r);break;case 24:Ea(i,r,u,f,n),n&&j&2048&&Br(r.alternate,r);break;default:Ea(i,r,u,f,n)}t=t.sibling}}function Tn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:Tn(l,a),n&2048&&Lr(a.alternate,a);break;case 24:Tn(l,a),n&2048&&Br(a.alternate,a);break;default:Tn(l,a)}t=t.sibling}}var En=8192;function za(e){if(e.subtreeFlags&En)for(e=e.child;e!==null;)zd(e),e=e.sibling}function zd(e){switch(e.tag){case 26:za(e),e.flags&En&&e.memoizedState!==null&&Th(kt,e.memoizedState,e.memoizedProps);break;case 5:za(e);break;case 3:case 4:var t=kt;kt=Js(e.stateNode.containerInfo),za(e),kt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=En,En=16777216,za(e),En=t):za(e));break;default:za(e)}}function _d(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function zn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];$e=a,Md(a,e)}_d(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ad(e),e=e.sibling}function Ad(e){switch(e.tag){case 0:case 11:case 15:zn(e),e.flags&2048&&vl(9,e,e.return);break;case 3:zn(e);break;case 12:zn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Rs(e)):zn(e);break;default:zn(e)}}function Rs(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];$e=a,Md(a,e)}_d(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:vl(8,t,t.return),Rs(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,Rs(t));break;default:Rs(t)}e=e.sibling}}function Md(e,t){for(;$e!==null;){var l=$e;switch(l.tag){case 0:case 11:case 15:vl(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:un(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,$e=a;else e:for(l=e;$e!==null;){a=$e;var n=a.sibling,i=a.return;if(jd(a),a===l){$e=null;break e}if(n!==null){n.return=i,$e=n;break e}$e=i}}}var Y0={getCacheForType:function(e){var t=lt(Xe),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},G0=typeof WeakMap=="function"?WeakMap:Map,we=0,Oe=null,me=null,ve=0,Te=0,yt=null,jl=!1,_a=!1,qr=!1,tl=0,He=0,Nl=0,$l=0,Yr=0,At=0,Aa=0,_n=null,ut=null,Gr=!1,Qr=0,Hs=1/0,Ls=null,Sl=null,Ie=0,wl=null,Ma=null,Ca=0,Xr=0,Vr=null,Cd=null,An=0,Zr=null;function bt(){if((we&2)!==0&&ve!==0)return ve&-ve;if(N.T!==null){var e=va;return e!==0?e:Pr()}return Zc()}function kd(){At===0&&(At=(ve&536870912)===0||Se?Gc():536870912);var e=_t.current;return e!==null&&(e.flags|=32),At}function jt(e,t,l){(e===Oe&&(Te===2||Te===9)||e.cancelPendingCommit!==null)&&(ka(e,0),Tl(e,ve,At,!1)),Za(e,l),((we&2)===0||e!==Oe)&&(e===Oe&&((we&2)===0&&($l|=l),He===4&&Tl(e,ve,At,!1)),Yt(e))}function Od(e,t,l){if((we&6)!==0)throw Error(c(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||Va(e,t),n=a?V0(e,t):$r(e,t,!0),i=a;do{if(n===0){_a&&!a&&Tl(e,t,0,!1);break}else{if(l=e.current.alternate,i&&!Q0(l)){n=$r(e,t,!1),i=!1;continue}if(n===2){if(i=t,e.errorRecoveryDisabledLanes&i)var r=0;else r=e.pendingLanes&-536870913,r=r!==0?r:r&536870912?536870912:0;if(r!==0){t=r;e:{var u=e;n=_n;var f=u.current.memoizedState.isDehydrated;if(f&&(ka(u,r).flags|=256),r=$r(u,r,!1),r!==2){if(qr&&!f){u.errorRecoveryDisabledLanes|=i,$l|=i,n=4;break e}i=ut,ut=n,i!==null&&(ut===null?ut=i:ut.push.apply(ut,i))}n=r}if(i=!1,n!==2)continue}}if(n===1){ka(e,0),Tl(e,t,0,!0);break}e:{switch(a=e,i=n,i){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Tl(a,t,At,!jl);break e;case 2:ut=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(n=Qr+300-Rt(),10<n)){if(Tl(a,t,At,!jl),$n(a,0,!0)!==0)break e;a.timeoutHandle=uf(Dd.bind(null,a,l,ut,Ls,Gr,t,At,$l,Aa,jl,i,2,-0,0),n);break e}Dd(a,l,ut,Ls,Gr,t,At,$l,Aa,jl,i,0,-0,0)}}break}while(!0);Yt(e)}function Dd(e,t,l,a,n,i,r,u,f,j,_,C,w,E){if(e.timeoutHandle=-1,C=t.subtreeFlags,(C&8192||(C&16785408)===16785408)&&(Rn={stylesheets:null,count:0,unsuspend:wh},zd(t),C=Eh(),C!==null)){e.cancelPendingCommit=C(Yd.bind(null,e,t,i,l,a,n,r,u,f,_,1,w,E)),Tl(e,i,r,!j);return}Yd(e,t,i,l,a,n,r,u,f)}function Q0(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!xt(i(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Tl(e,t,l,a){t&=~Yr,t&=~$l,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var i=31-ht(n),r=1<<i;a[i]=-1,n&=~r}l!==0&&Xc(e,l,t)}function Bs(){return(we&6)===0?(Mn(0),!1):!0}function Kr(){if(me!==null){if(Te===0)var e=me.return;else e=me,Kt=Ql=null,or(e),wa=null,bn=0,e=me;for(;e!==null;)md(e.alternate,e),e=e.return;me=null}}function ka(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,rh(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Kr(),Oe=e,me=l=Xt(e.current,null),ve=t,Te=0,yt=null,jl=!1,_a=Va(e,t),qr=!1,Aa=At=Yr=$l=Nl=He=0,ut=_n=null,Gr=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-ht(a),i=1<<n;t|=e[n],a&=~i}return tl=t,rs(),l}function Ud(e,t){ue=null,N.H=Es,t===dn||t===gs?(t=Fu(),Te=3):t===Ju?(t=Fu(),Te=4):Te=t===Po?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,yt=t,me===null&&(He=1,Cs(e,wt(t,e.current)))}function Rd(){var e=N.H;return N.H=Es,e===null?Es:e}function Hd(){var e=N.A;return N.A=Y0,e}function Jr(){He=4,jl||(ve&4194048)!==ve&&_t.current!==null||(_a=!0),(Nl&134217727)===0&&($l&134217727)===0||Oe===null||Tl(Oe,ve,At,!1)}function $r(e,t,l){var a=we;we|=2;var n=Rd(),i=Hd();(Oe!==e||ve!==t)&&(Ls=null,ka(e,t)),t=!1;var r=He;e:do try{if(Te!==0&&me!==null){var u=me,f=yt;switch(Te){case 8:Kr(),r=6;break e;case 3:case 2:case 9:case 6:_t.current===null&&(t=!0);var j=Te;if(Te=0,yt=null,Oa(e,u,f,j),l&&_a){r=0;break e}break;default:j=Te,Te=0,yt=null,Oa(e,u,f,j)}}X0(),r=He;break}catch(_){Ud(e,_)}while(!0);return t&&e.shellSuspendCounter++,Kt=Ql=null,we=a,N.H=n,N.A=i,me===null&&(Oe=null,ve=0,rs()),r}function X0(){for(;me!==null;)Ld(me)}function V0(e,t){var l=we;we|=2;var a=Rd(),n=Hd();Oe!==e||ve!==t?(Ls=null,Hs=Rt()+500,ka(e,t)):_a=Va(e,t);e:do try{if(Te!==0&&me!==null){t=me;var i=yt;t:switch(Te){case 1:Te=0,yt=null,Oa(e,t,i,1);break;case 2:case 9:if($u(i)){Te=0,yt=null,Bd(t);break}t=function(){Te!==2&&Te!==9||Oe!==e||(Te=7),Yt(e)},i.then(t,t);break e;case 3:Te=7;break e;case 4:Te=5;break e;case 7:$u(i)?(Te=0,yt=null,Bd(t)):(Te=0,yt=null,Oa(e,t,i,7));break;case 5:var r=null;switch(me.tag){case 26:r=me.memoizedState;case 5:case 27:var u=me;if(!r||jf(r)){Te=0,yt=null;var f=u.sibling;if(f!==null)me=f;else{var j=u.return;j!==null?(me=j,qs(j)):me=null}break t}}Te=0,yt=null,Oa(e,t,i,5);break;case 6:Te=0,yt=null,Oa(e,t,i,6);break;case 8:Kr(),He=6;break e;default:throw Error(c(462))}}Z0();break}catch(_){Ud(e,_)}while(!0);return Kt=Ql=null,N.H=a,N.A=n,we=l,me!==null?0:(Oe=null,ve=0,rs(),He)}function Z0(){for(;me!==null&&!hm();)Ld(me)}function Ld(e){var t=dd(e.alternate,e,tl);e.memoizedProps=e.pendingProps,t===null?qs(e):me=t}function Bd(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=sd(l,t,t.pendingProps,t.type,void 0,ve);break;case 11:t=sd(l,t,t.pendingProps,t.type.render,t.ref,ve);break;case 5:or(t);default:md(l,t),t=me=Bu(t,tl),t=dd(l,t,tl)}e.memoizedProps=e.pendingProps,t===null?qs(e):me=t}function Oa(e,t,l,a){Kt=Ql=null,or(t),wa=null,bn=0;var n=t.return;try{if(U0(e,n,t,l,ve)){He=1,Cs(e,wt(l,e.current)),me=null;return}}catch(i){if(n!==null)throw me=n,i;He=1,Cs(e,wt(l,e.current)),me=null;return}t.flags&32768?(Se||a===1?e=!0:_a||(ve&536870912)!==0?e=!1:(jl=e=!0,(a===2||a===9||a===3||a===6)&&(a=_t.current,a!==null&&a.tag===13&&(a.flags|=16384))),qd(t,e)):qs(t)}function qs(e){var t=e;do{if((t.flags&32768)!==0){qd(t,jl);return}e=t.return;var l=H0(t.alternate,t,tl);if(l!==null){me=l;return}if(t=t.sibling,t!==null){me=t;return}me=t=e}while(t!==null);He===0&&(He=5)}function qd(e,t){do{var l=L0(e.alternate,e);if(l!==null){l.flags&=32767,me=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){me=e;return}me=e=l}while(e!==null);He=6,me=null}function Yd(e,t,l,a,n,i,r,u,f){e.cancelPendingCommit=null;do Ys();while(Ie!==0);if((we&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(i=t.lanes|t.childLanes,i|=Bi,wm(e,l,i,r,u,f),e===Oe&&(me=Oe=null,ve=0),Ma=t,wl=e,Ca=l,Xr=i,Vr=n,Cd=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,W0(Zn,function(){return Zd(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=N.T,N.T=null,n=q.p,q.p=2,r=we,we|=4;try{B0(e,t,l)}finally{we=r,q.p=n,N.T=a}}Ie=1,Gd(),Qd(),Xd()}}function Gd(){if(Ie===1){Ie=0;var e=wl,t=Ma,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=N.T,N.T=null;var a=q.p;q.p=2;var n=we;we|=4;try{wd(t,e);var i=rc,r=Au(e.containerInfo),u=i.focusedElem,f=i.selectionRange;if(r!==u&&u&&u.ownerDocument&&_u(u.ownerDocument.documentElement,u)){if(f!==null&&Di(u)){var j=f.start,_=f.end;if(_===void 0&&(_=j),"selectionStart"in u)u.selectionStart=j,u.selectionEnd=Math.min(_,u.value.length);else{var C=u.ownerDocument||document,w=C&&C.defaultView||window;if(w.getSelection){var E=w.getSelection(),ne=u.textContent.length,te=Math.min(f.start,ne),_e=f.end===void 0?te:Math.min(f.end,ne);!E.extend&&te>_e&&(r=_e,_e=te,te=r);var v=zu(u,te),x=zu(u,_e);if(v&&x&&(E.rangeCount!==1||E.anchorNode!==v.node||E.anchorOffset!==v.offset||E.focusNode!==x.node||E.focusOffset!==x.offset)){var b=C.createRange();b.setStart(v.node,v.offset),E.removeAllRanges(),te>_e?(E.addRange(b),E.extend(x.node,x.offset)):(b.setEnd(x.node,x.offset),E.addRange(b))}}}}for(C=[],E=u;E=E.parentNode;)E.nodeType===1&&C.push({element:E,left:E.scrollLeft,top:E.scrollTop});for(typeof u.focus=="function"&&u.focus(),u=0;u<C.length;u++){var A=C[u];A.element.scrollLeft=A.left,A.element.scrollTop=A.top}}Ps=!!ic,rc=ic=null}finally{we=n,q.p=a,N.T=l}}e.current=t,Ie=2}}function Qd(){if(Ie===2){Ie=0;var e=wl,t=Ma,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=N.T,N.T=null;var a=q.p;q.p=2;var n=we;we|=4;try{bd(e,t.alternate,t)}finally{we=n,q.p=a,N.T=l}}Ie=3}}function Xd(){if(Ie===4||Ie===3){Ie=0,xm();var e=wl,t=Ma,l=Ca,a=Cd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ie=5:(Ie=0,Ma=wl=null,Vd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Sl=null),mi(l),t=t.stateNode,mt&&typeof mt.onCommitFiberRoot=="function")try{mt.onCommitFiberRoot(Xa,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=N.T,n=q.p,q.p=2,N.T=null;try{for(var i=e.onRecoverableError,r=0;r<a.length;r++){var u=a[r];i(u.value,{componentStack:u.stack})}}finally{N.T=t,q.p=n}}(Ca&3)!==0&&Ys(),Yt(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===Zr?An++:(An=0,Zr=e):An=0,Mn(0)}}function Vd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,un(t)))}function Ys(e){return Gd(),Qd(),Xd(),Zd()}function Zd(){if(Ie!==5)return!1;var e=wl,t=Xr;Xr=0;var l=mi(Ca),a=N.T,n=q.p;try{q.p=32>l?32:l,N.T=null,l=Vr,Vr=null;var i=wl,r=Ca;if(Ie=0,Ma=wl=null,Ca=0,(we&6)!==0)throw Error(c(331));var u=we;if(we|=4,Ad(i.current),Ed(i,i.current,r,l),we=u,Mn(0,!1),mt&&typeof mt.onPostCommitFiberRoot=="function")try{mt.onPostCommitFiberRoot(Xa,i)}catch{}return!0}finally{q.p=n,N.T=a,Vd(e,t)}}function Kd(e,t,l){t=wt(l,t),t=wr(e.stateNode,t,2),e=hl(e,t,2),e!==null&&(Za(e,2),Yt(e))}function Me(e,t,l){if(e.tag===3)Kd(e,e,l);else for(;t!==null;){if(t.tag===3){Kd(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Sl===null||!Sl.has(a))){e=wt(l,e),l=Fo(2),a=hl(t,l,2),a!==null&&(Io(l,a,t,e),Za(a,2),Yt(a));break}}t=t.return}}function Wr(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new G0;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(qr=!0,n.add(l),e=K0.bind(null,e,t,l),t.then(e,e))}function K0(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Oe===e&&(ve&l)===l&&(He===4||He===3&&(ve&62914560)===ve&&300>Rt()-Qr?(we&2)===0&&ka(e,0):Yr|=l,Aa===ve&&(Aa=0)),Yt(e)}function Jd(e,t){t===0&&(t=Qc()),e=ha(e,t),e!==null&&(Za(e,t),Yt(e))}function J0(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),Jd(e,l)}function $0(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(c(314))}a!==null&&a.delete(t),Jd(e,l)}function W0(e,t){return ui(e,t)}var Gs=null,Da=null,Fr=!1,Qs=!1,Ir=!1,Wl=0;function Yt(e){e!==Da&&e.next===null&&(Da===null?Gs=Da=e:Da=Da.next=e),Qs=!0,Fr||(Fr=!0,I0())}function Mn(e,t){if(!Ir&&Qs){Ir=!0;do for(var l=!1,a=Gs;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var r=a.suspendedLanes,u=a.pingedLanes;i=(1<<31-ht(42|e)+1)-1,i&=n&~(r&~u),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,Id(a,i))}else i=ve,i=$n(a,a===Oe?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||Va(a,i)||(l=!0,Id(a,i));a=a.next}while(l);Ir=!1}}function F0(){$d()}function $d(){Qs=Fr=!1;var e=0;Wl!==0&&(ih()&&(e=Wl),Wl=0);for(var t=Rt(),l=null,a=Gs;a!==null;){var n=a.next,i=Wd(a,t);i===0?(a.next=null,l===null?Gs=n:l.next=n,n===null&&(Da=l)):(l=a,(e!==0||(i&3)!==0)&&(Qs=!0)),a=n}Mn(e)}function Wd(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var r=31-ht(i),u=1<<r,f=n[r];f===-1?((u&l)===0||(u&a)!==0)&&(n[r]=Sm(u,t)):f<=t&&(e.expiredLanes|=u),i&=~u}if(t=Oe,l=ve,l=$n(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(Te===2||Te===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&oi(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||Va(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&oi(a),mi(l)){case 2:case 8:l=qc;break;case 32:l=Zn;break;case 268435456:l=Yc;break;default:l=Zn}return a=Fd.bind(null,e),l=ui(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&oi(a),e.callbackPriority=2,e.callbackNode=null,2}function Fd(e,t){if(Ie!==0&&Ie!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(Ys()&&e.callbackNode!==l)return null;var a=ve;return a=$n(e,e===Oe?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Od(e,a,t),Wd(e,Rt()),e.callbackNode!=null&&e.callbackNode===l?Fd.bind(null,e):null)}function Id(e,t){if(Ys())return null;Od(e,t,!0)}function I0(){ch(function(){(we&6)!==0?ui(Bc,F0):$d()})}function Pr(){return Wl===0&&(Wl=Gc()),Wl}function Pd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:es(""+e)}function ef(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function P0(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var i=Pd((n[st]||null).action),r=a.submitter;r&&(t=(t=r[st]||null)?Pd(t.formAction):r.getAttribute("formAction"),t!==null&&(i=t,r=null));var u=new ns("action","action",null,a,n);e.push({event:u,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Wl!==0){var f=r?ef(n,r):new FormData(n);yr(l,{pending:!0,data:f,method:n.method,action:i},null,f)}}else typeof i=="function"&&(u.preventDefault(),f=r?ef(n,r):new FormData(n),yr(l,{pending:!0,data:f,method:n.method,action:i},i,f))},currentTarget:n}]})}}for(var ec=0;ec<Li.length;ec++){var tc=Li[ec],eh=tc.toLowerCase(),th=tc[0].toUpperCase()+tc.slice(1);Ct(eh,"on"+th)}Ct(ku,"onAnimationEnd"),Ct(Ou,"onAnimationIteration"),Ct(Du,"onAnimationStart"),Ct("dblclick","onDoubleClick"),Ct("focusin","onFocus"),Ct("focusout","onBlur"),Ct(v0,"onTransitionRun"),Ct(y0,"onTransitionStart"),Ct(b0,"onTransitionCancel"),Ct(Uu,"onTransitionEnd"),na("onMouseEnter",["mouseout","mouseover"]),na("onMouseLeave",["mouseout","mouseover"]),na("onPointerEnter",["pointerout","pointerover"]),na("onPointerLeave",["pointerout","pointerover"]),Dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Cn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),lh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Cn));function tf(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var i=void 0;if(t)for(var r=a.length-1;0<=r;r--){var u=a[r],f=u.instance,j=u.currentTarget;if(u=u.listener,f!==i&&n.isPropagationStopped())break e;i=u,n.currentTarget=j;try{i(n)}catch(_){Ms(_)}n.currentTarget=null,i=f}else for(r=0;r<a.length;r++){if(u=a[r],f=u.instance,j=u.currentTarget,u=u.listener,f!==i&&n.isPropagationStopped())break e;i=u,n.currentTarget=j;try{i(n)}catch(_){Ms(_)}n.currentTarget=null,i=f}}}}function he(e,t){var l=t[hi];l===void 0&&(l=t[hi]=new Set);var a=e+"__bubble";l.has(a)||(lf(t,e,2,!1),l.add(a))}function lc(e,t,l){var a=0;t&&(a|=4),lf(l,e,a,t)}var Xs="_reactListening"+Math.random().toString(36).slice(2);function ac(e){if(!e[Xs]){e[Xs]=!0,Jc.forEach(function(l){l!=="selectionchange"&&(lh.has(l)||lc(l,!1,e),lc(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xs]||(t[Xs]=!0,lc("selectionchange",!1,t))}}function lf(e,t,l,a){switch(zf(t)){case 2:var n=Ah;break;case 8:n=Mh;break;default:n=pc}l=n.bind(null,t,l,e),n=void 0,!Ti||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function nc(e,t,l,a,n){var i=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var r=a.tag;if(r===3||r===4){var u=a.stateNode.containerInfo;if(u===n)break;if(r===4)for(r=a.return;r!==null;){var f=r.tag;if((f===3||f===4)&&r.stateNode.containerInfo===n)return;r=r.return}for(;u!==null;){if(r=ta(u),r===null)return;if(f=r.tag,f===5||f===6||f===26||f===27){a=i=r;continue e}u=u.parentNode}}a=a.return}cu(function(){var j=i,_=Si(l),C=[];e:{var w=Ru.get(e);if(w!==void 0){var E=ns,ne=e;switch(e){case"keypress":if(ls(l)===0)break e;case"keydown":case"keyup":E=Wm;break;case"focusin":ne="focus",E=Ai;break;case"focusout":ne="blur",E=Ai;break;case"beforeblur":case"afterblur":E=Ai;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":E=du;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":E=Lm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":E=Pm;break;case ku:case Ou:case Du:E=Ym;break;case Uu:E=t0;break;case"scroll":case"scrollend":E=Rm;break;case"wheel":E=a0;break;case"copy":case"cut":case"paste":E=Qm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":E=mu;break;case"toggle":case"beforetoggle":E=s0}var te=(t&4)!==0,_e=!te&&(e==="scroll"||e==="scrollend"),v=te?w!==null?w+"Capture":null:w;te=[];for(var x=j,b;x!==null;){var A=x;if(b=A.stateNode,A=A.tag,A!==5&&A!==26&&A!==27||b===null||v===null||(A=$a(x,v),A!=null&&te.push(kn(x,A,b))),_e)break;x=x.return}0<te.length&&(w=new E(w,ne,null,l,_),C.push({event:w,listeners:te}))}}if((t&7)===0){e:{if(w=e==="mouseover"||e==="pointerover",E=e==="mouseout"||e==="pointerout",w&&l!==Ni&&(ne=l.relatedTarget||l.fromElement)&&(ta(ne)||ne[ea]))break e;if((E||w)&&(w=_.window===_?_:(w=_.ownerDocument)?w.defaultView||w.parentWindow:window,E?(ne=l.relatedTarget||l.toElement,E=j,ne=ne?ta(ne):null,ne!==null&&(_e=z(ne),te=ne.tag,ne!==_e||te!==5&&te!==27&&te!==6)&&(ne=null)):(E=null,ne=j),E!==ne)){if(te=du,A="onMouseLeave",v="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(te=mu,A="onPointerLeave",v="onPointerEnter",x="pointer"),_e=E==null?w:Ja(E),b=ne==null?w:Ja(ne),w=new te(A,x+"leave",E,l,_),w.target=_e,w.relatedTarget=b,A=null,ta(_)===j&&(te=new te(v,x+"enter",ne,l,_),te.target=b,te.relatedTarget=_e,A=te),_e=A,E&&ne)t:{for(te=E,v=ne,x=0,b=te;b;b=Ua(b))x++;for(b=0,A=v;A;A=Ua(A))b++;for(;0<x-b;)te=Ua(te),x--;for(;0<b-x;)v=Ua(v),b--;for(;x--;){if(te===v||v!==null&&te===v.alternate)break t;te=Ua(te),v=Ua(v)}te=null}else te=null;E!==null&&af(C,w,E,te,!1),ne!==null&&_e!==null&&af(C,_e,ne,te,!0)}}e:{if(w=j?Ja(j):window,E=w.nodeName&&w.nodeName.toLowerCase(),E==="select"||E==="input"&&w.type==="file")var J=ju;else if(yu(w))if(Nu)J=x0;else{J=m0;var de=f0}else E=w.nodeName,!E||E.toLowerCase()!=="input"||w.type!=="checkbox"&&w.type!=="radio"?j&&ji(j.elementType)&&(J=ju):J=h0;if(J&&(J=J(e,j))){bu(C,J,l,_);break e}de&&de(e,w,j),e==="focusout"&&j&&w.type==="number"&&j.memoizedProps.value!=null&&bi(w,"number",w.value)}switch(de=j?Ja(j):window,e){case"focusin":(yu(de)||de.contentEditable==="true")&&(da=de,Ui=j,an=null);break;case"focusout":an=Ui=da=null;break;case"mousedown":Ri=!0;break;case"contextmenu":case"mouseup":case"dragend":Ri=!1,Mu(C,l,_);break;case"selectionchange":if(p0)break;case"keydown":case"keyup":Mu(C,l,_)}var I;if(Ci)e:{switch(e){case"compositionstart":var ae="onCompositionStart";break e;case"compositionend":ae="onCompositionEnd";break e;case"compositionupdate":ae="onCompositionUpdate";break e}ae=void 0}else oa?pu(e,l)&&(ae="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(ae="onCompositionStart");ae&&(hu&&l.locale!=="ko"&&(oa||ae!=="onCompositionStart"?ae==="onCompositionEnd"&&oa&&(I=uu()):(ol=_,Ei="value"in ol?ol.value:ol.textContent,oa=!0)),de=Vs(j,ae),0<de.length&&(ae=new fu(ae,e,null,l,_),C.push({event:ae,listeners:de}),I?ae.data=I:(I=vu(l),I!==null&&(ae.data=I)))),(I=r0?c0(e,l):u0(e,l))&&(ae=Vs(j,"onBeforeInput"),0<ae.length&&(de=new fu("onBeforeInput","beforeinput",null,l,_),C.push({event:de,listeners:ae}),de.data=I)),P0(C,e,j,l,_)}tf(C,t)})}function kn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function Vs(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=$a(e,l),n!=null&&a.unshift(kn(e,n,i)),n=$a(e,t),n!=null&&a.push(kn(e,n,i))),e.tag===3)return a;e=e.return}return[]}function Ua(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function af(e,t,l,a,n){for(var i=t._reactName,r=[];l!==null&&l!==a;){var u=l,f=u.alternate,j=u.stateNode;if(u=u.tag,f!==null&&f===a)break;u!==5&&u!==26&&u!==27||j===null||(f=j,n?(j=$a(l,i),j!=null&&r.unshift(kn(l,j,f))):n||(j=$a(l,i),j!=null&&r.push(kn(l,j,f)))),l=l.return}r.length!==0&&e.push({event:t,listeners:r})}var ah=/\r\n?/g,nh=/\u0000|\uFFFD/g;function nf(e){return(typeof e=="string"?e:""+e).replace(ah,`
`).replace(nh,"")}function sf(e,t){return t=nf(t),nf(e)===t}function Zs(){}function ze(e,t,l,a,n,i){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||ra(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&ra(e,""+a);break;case"className":Fn(e,"class",a);break;case"tabIndex":Fn(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Fn(e,l,a);break;case"style":iu(e,a,i);break;case"data":if(t!=="object"){Fn(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=es(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(t!=="input"&&ze(e,t,"name",n.name,n,null),ze(e,t,"formEncType",n.formEncType,n,null),ze(e,t,"formMethod",n.formMethod,n,null),ze(e,t,"formTarget",n.formTarget,n,null)):(ze(e,t,"encType",n.encType,n,null),ze(e,t,"method",n.method,n,null),ze(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=es(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=Zs);break;case"onScroll":a!=null&&he("scroll",e);break;case"onScrollEnd":a!=null&&he("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=es(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":he("beforetoggle",e),he("toggle",e),Wn(e,"popover",a);break;case"xlinkActuate":Gt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Gt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Gt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Gt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Gt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Gt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Gt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Gt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Gt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Wn(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Dm.get(l)||l,Wn(e,l,a))}}function sc(e,t,l,a,n,i){switch(l){case"style":iu(e,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"children":typeof a=="string"?ra(e,a):(typeof a=="number"||typeof a=="bigint")&&ra(e,""+a);break;case"onScroll":a!=null&&he("scroll",e);break;case"onScrollEnd":a!=null&&he("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Zs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!$c.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),i=e[st]||null,i=i!=null?i[l]:null,typeof i=="function"&&e.removeEventListener(t,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Wn(e,l,a)}}}function Pe(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":he("error",e),he("load",e);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var r=l[i];if(r!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:ze(e,t,i,r,l,null)}}n&&ze(e,t,"srcSet",l.srcSet,l,null),a&&ze(e,t,"src",l.src,l,null);return;case"input":he("invalid",e);var u=i=r=n=null,f=null,j=null;for(a in l)if(l.hasOwnProperty(a)){var _=l[a];if(_!=null)switch(a){case"name":n=_;break;case"type":r=_;break;case"checked":f=_;break;case"defaultChecked":j=_;break;case"value":i=_;break;case"defaultValue":u=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(c(137,t));break;default:ze(e,t,a,_,l,null)}}lu(e,i,u,f,j,r,n,!1),In(e);return;case"select":he("invalid",e),a=r=i=null;for(n in l)if(l.hasOwnProperty(n)&&(u=l[n],u!=null))switch(n){case"value":i=u;break;case"defaultValue":r=u;break;case"multiple":a=u;default:ze(e,t,n,u,l,null)}t=i,l=r,e.multiple=!!a,t!=null?ia(e,!!a,t,!1):l!=null&&ia(e,!!a,l,!0);return;case"textarea":he("invalid",e),i=n=a=null;for(r in l)if(l.hasOwnProperty(r)&&(u=l[r],u!=null))switch(r){case"value":a=u;break;case"defaultValue":n=u;break;case"children":i=u;break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(c(91));break;default:ze(e,t,r,u,l,null)}nu(e,a,n,i),In(e);return;case"option":for(f in l)if(l.hasOwnProperty(f)&&(a=l[f],a!=null))switch(f){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:ze(e,t,f,a,l,null)}return;case"dialog":he("beforetoggle",e),he("toggle",e),he("cancel",e),he("close",e);break;case"iframe":case"object":he("load",e);break;case"video":case"audio":for(a=0;a<Cn.length;a++)he(Cn[a],e);break;case"image":he("error",e),he("load",e);break;case"details":he("toggle",e);break;case"embed":case"source":case"link":he("error",e),he("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(j in l)if(l.hasOwnProperty(j)&&(a=l[j],a!=null))switch(j){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:ze(e,t,j,a,l,null)}return;default:if(ji(t)){for(_ in l)l.hasOwnProperty(_)&&(a=l[_],a!==void 0&&sc(e,t,_,a,l,void 0));return}}for(u in l)l.hasOwnProperty(u)&&(a=l[u],a!=null&&ze(e,t,u,a,l,null))}function sh(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,r=null,u=null,f=null,j=null,_=null;for(E in l){var C=l[E];if(l.hasOwnProperty(E)&&C!=null)switch(E){case"checked":break;case"value":break;case"defaultValue":f=C;default:a.hasOwnProperty(E)||ze(e,t,E,null,a,C)}}for(var w in a){var E=a[w];if(C=l[w],a.hasOwnProperty(w)&&(E!=null||C!=null))switch(w){case"type":i=E;break;case"name":n=E;break;case"checked":j=E;break;case"defaultChecked":_=E;break;case"value":r=E;break;case"defaultValue":u=E;break;case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(c(137,t));break;default:E!==C&&ze(e,t,w,E,a,C)}}yi(e,r,u,f,j,_,i,n);return;case"select":E=r=u=w=null;for(i in l)if(f=l[i],l.hasOwnProperty(i)&&f!=null)switch(i){case"value":break;case"multiple":E=f;default:a.hasOwnProperty(i)||ze(e,t,i,null,a,f)}for(n in a)if(i=a[n],f=l[n],a.hasOwnProperty(n)&&(i!=null||f!=null))switch(n){case"value":w=i;break;case"defaultValue":u=i;break;case"multiple":r=i;default:i!==f&&ze(e,t,n,i,a,f)}t=u,l=r,a=E,w!=null?ia(e,!!l,w,!1):!!a!=!!l&&(t!=null?ia(e,!!l,t,!0):ia(e,!!l,l?[]:"",!1));return;case"textarea":E=w=null;for(u in l)if(n=l[u],l.hasOwnProperty(u)&&n!=null&&!a.hasOwnProperty(u))switch(u){case"value":break;case"children":break;default:ze(e,t,u,null,a,n)}for(r in a)if(n=a[r],i=l[r],a.hasOwnProperty(r)&&(n!=null||i!=null))switch(r){case"value":w=n;break;case"defaultValue":E=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(c(91));break;default:n!==i&&ze(e,t,r,n,a,i)}au(e,w,E);return;case"option":for(var ne in l)if(w=l[ne],l.hasOwnProperty(ne)&&w!=null&&!a.hasOwnProperty(ne))switch(ne){case"selected":e.selected=!1;break;default:ze(e,t,ne,null,a,w)}for(f in a)if(w=a[f],E=l[f],a.hasOwnProperty(f)&&w!==E&&(w!=null||E!=null))switch(f){case"selected":e.selected=w&&typeof w!="function"&&typeof w!="symbol";break;default:ze(e,t,f,w,a,E)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var te in l)w=l[te],l.hasOwnProperty(te)&&w!=null&&!a.hasOwnProperty(te)&&ze(e,t,te,null,a,w);for(j in a)if(w=a[j],E=l[j],a.hasOwnProperty(j)&&w!==E&&(w!=null||E!=null))switch(j){case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(c(137,t));break;default:ze(e,t,j,w,a,E)}return;default:if(ji(t)){for(var _e in l)w=l[_e],l.hasOwnProperty(_e)&&w!==void 0&&!a.hasOwnProperty(_e)&&sc(e,t,_e,void 0,a,w);for(_ in a)w=a[_],E=l[_],!a.hasOwnProperty(_)||w===E||w===void 0&&E===void 0||sc(e,t,_,w,a,E);return}}for(var v in l)w=l[v],l.hasOwnProperty(v)&&w!=null&&!a.hasOwnProperty(v)&&ze(e,t,v,null,a,w);for(C in a)w=a[C],E=l[C],!a.hasOwnProperty(C)||w===E||w==null&&E==null||ze(e,t,C,w,a,E)}var ic=null,rc=null;function Ks(e){return e.nodeType===9?e:e.ownerDocument}function rf(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function cf(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function cc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var uc=null;function ih(){var e=window.event;return e&&e.type==="popstate"?e===uc?!1:(uc=e,!0):(uc=null,!1)}var uf=typeof setTimeout=="function"?setTimeout:void 0,rh=typeof clearTimeout=="function"?clearTimeout:void 0,of=typeof Promise=="function"?Promise:void 0,ch=typeof queueMicrotask=="function"?queueMicrotask:typeof of<"u"?function(e){return of.resolve(null).then(e).catch(uh)}:uf;function uh(e){setTimeout(function(){throw e})}function El(e){return e==="head"}function df(e,t){var l=t,a=0,n=0;do{var i=l.nextSibling;if(e.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var r=e.ownerDocument;if(l&1&&On(r.documentElement),l&2&&On(r.body),l&4)for(l=r.head,On(l),r=l.firstChild;r;){var u=r.nextSibling,f=r.nodeName;r[Ka]||f==="SCRIPT"||f==="STYLE"||f==="LINK"&&r.rel.toLowerCase()==="stylesheet"||l.removeChild(r),r=u}}if(n===0){e.removeChild(i),Yn(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);Yn(t)}function oc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":oc(l),xi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function oh(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ka])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=Ot(e.nextSibling),e===null)break}return null}function dh(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Ot(e.nextSibling),e===null))return null;return e}function dc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function fh(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Ot(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var fc=null;function ff(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function mf(e,t,l){switch(t=Ks(l),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function On(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);xi(e)}var Mt=new Map,hf=new Set;function Js(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ll=q.d;q.d={f:mh,r:hh,D:xh,C:gh,L:ph,m:vh,X:bh,S:yh,M:jh};function mh(){var e=ll.f(),t=Bs();return e||t}function hh(e){var t=la(e);t!==null&&t.tag===5&&t.type==="form"?Oo(t):ll.r(e)}var Ra=typeof document>"u"?null:document;function xf(e,t,l){var a=Ra;if(a&&typeof t=="string"&&t){var n=St(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),hf.has(n)||(hf.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),Pe(t,"link",e),Ke(t),a.head.appendChild(t)))}}function xh(e){ll.D(e),xf("dns-prefetch",e,null)}function gh(e,t){ll.C(e,t),xf("preconnect",e,t)}function ph(e,t,l){ll.L(e,t,l);var a=Ra;if(a&&e&&t){var n='link[rel="preload"][as="'+St(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+St(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+St(l.imageSizes)+'"]')):n+='[href="'+St(e)+'"]';var i=n;switch(t){case"style":i=Ha(e);break;case"script":i=La(e)}Mt.has(i)||(e=S({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),Mt.set(i,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(Dn(i))||t==="script"&&a.querySelector(Un(i))||(t=a.createElement("link"),Pe(t,"link",e),Ke(t),a.head.appendChild(t)))}}function vh(e,t){ll.m(e,t);var l=Ra;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+St(a)+'"][href="'+St(e)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=La(e)}if(!Mt.has(i)&&(e=S({rel:"modulepreload",href:e},t),Mt.set(i,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Un(i)))return}a=l.createElement("link"),Pe(a,"link",e),Ke(a),l.head.appendChild(a)}}}function yh(e,t,l){ll.S(e,t,l);var a=Ra;if(a&&e){var n=aa(a).hoistableStyles,i=Ha(e);t=t||"default";var r=n.get(i);if(!r){var u={loading:0,preload:null};if(r=a.querySelector(Dn(i)))u.loading=5;else{e=S({rel:"stylesheet",href:e,"data-precedence":t},l),(l=Mt.get(i))&&mc(e,l);var f=r=a.createElement("link");Ke(f),Pe(f,"link",e),f._p=new Promise(function(j,_){f.onload=j,f.onerror=_}),f.addEventListener("load",function(){u.loading|=1}),f.addEventListener("error",function(){u.loading|=2}),u.loading|=4,$s(r,t,a)}r={type:"stylesheet",instance:r,count:1,state:u},n.set(i,r)}}}function bh(e,t){ll.X(e,t);var l=Ra;if(l&&e){var a=aa(l).hoistableScripts,n=La(e),i=a.get(n);i||(i=l.querySelector(Un(n)),i||(e=S({src:e,async:!0},t),(t=Mt.get(n))&&hc(e,t),i=l.createElement("script"),Ke(i),Pe(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function jh(e,t){ll.M(e,t);var l=Ra;if(l&&e){var a=aa(l).hoistableScripts,n=La(e),i=a.get(n);i||(i=l.querySelector(Un(n)),i||(e=S({src:e,async:!0,type:"module"},t),(t=Mt.get(n))&&hc(e,t),i=l.createElement("script"),Ke(i),Pe(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function gf(e,t,l,a){var n=(n=se.current)?Js(n):null;if(!n)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Ha(l.href),l=aa(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Ha(l.href);var i=aa(n).hoistableStyles,r=i.get(e);if(r||(n=n.ownerDocument||n,r={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,r),(i=n.querySelector(Dn(e)))&&!i._p&&(r.instance=i,r.state.loading=5),Mt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Mt.set(e,l),i||Nh(n,e,l,r.state))),t&&a===null)throw Error(c(528,""));return r}if(t&&a!==null)throw Error(c(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=La(l),l=aa(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Ha(e){return'href="'+St(e)+'"'}function Dn(e){return'link[rel="stylesheet"]['+e+"]"}function pf(e){return S({},e,{"data-precedence":e.precedence,precedence:null})}function Nh(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Pe(t,"link",l),Ke(t),e.head.appendChild(t))}function La(e){return'[src="'+St(e)+'"]'}function Un(e){return"script[async]"+e}function vf(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+St(l.href)+'"]');if(a)return t.instance=a,Ke(a),a;var n=S({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Ke(a),Pe(a,"style",n),$s(a,l.precedence,e),t.instance=a;case"stylesheet":n=Ha(l.href);var i=e.querySelector(Dn(n));if(i)return t.state.loading|=4,t.instance=i,Ke(i),i;a=pf(l),(n=Mt.get(n))&&mc(a,n),i=(e.ownerDocument||e).createElement("link"),Ke(i);var r=i;return r._p=new Promise(function(u,f){r.onload=u,r.onerror=f}),Pe(i,"link",a),t.state.loading|=4,$s(i,l.precedence,e),t.instance=i;case"script":return i=La(l.src),(n=e.querySelector(Un(i)))?(t.instance=n,Ke(n),n):(a=l,(n=Mt.get(i))&&(a=S({},l),hc(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ke(n),Pe(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,$s(a,l.precedence,e));return t.instance}function $s(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,r=0;r<a.length;r++){var u=a[r];if(u.dataset.precedence===t)i=u;else if(i!==n)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function mc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function hc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ws=null;function yf(e,t,l){if(Ws===null){var a=new Map,n=Ws=new Map;n.set(l,a)}else n=Ws,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var i=l[n];if(!(i[Ka]||i[tt]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var r=i.getAttribute(t)||"";r=e+r;var u=a.get(r);u?u.push(i):a.set(r,[i])}}return a}function bf(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function Sh(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function jf(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Rn=null;function wh(){}function Th(e,t,l){if(Rn===null)throw Error(c(475));var a=Rn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Ha(l.href),i=e.querySelector(Dn(n));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Fs.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=i,Ke(i);return}i=e.ownerDocument||e,l=pf(l),(n=Mt.get(n))&&mc(l,n),i=i.createElement("link"),Ke(i);var r=i;r._p=new Promise(function(u,f){r.onload=u,r.onerror=f}),Pe(i,"link",l),t.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Fs.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Eh(){if(Rn===null)throw Error(c(475));var e=Rn;return e.stylesheets&&e.count===0&&xc(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&xc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function Fs(){if(this.count--,this.count===0){if(this.stylesheets)xc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Is=null;function xc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Is=new Map,t.forEach(zh,e),Is=null,Fs.call(e))}function zh(e,t){if(!(t.state.loading&4)){var l=Is.get(e);if(l)var a=l.get(null);else{l=new Map,Is.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var r=n[i];(r.nodeName==="LINK"||r.getAttribute("media")!=="not all")&&(l.set(r.dataset.precedence,r),a=r)}a&&l.set(null,a)}n=t.instance,r=n.getAttribute("data-precedence"),i=l.get(r)||a,i===a&&l.set(null,n),l.set(r,n),this.count++,a=Fs.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Hn={$$typeof:P,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function _h(e,t,l,a,n,i,r,u){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=di(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=di(0),this.hiddenUpdates=di(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=r,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function Nf(e,t,l,a,n,i,r,u,f,j,_,C){return e=new _h(e,t,l,r,u,f,j,C),t=1,i===!0&&(t|=24),i=gt(3,null,null,t),e.current=i,i.stateNode=e,t=Wi(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:t},er(i),e}function Sf(e){return e?(e=xa,e):xa}function wf(e,t,l,a,n,i){n=Sf(n),a.context===null?a.context=n:a.pendingContext=n,a=ml(t),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=hl(e,a,t),l!==null&&(jt(l,e,t),mn(l,e,t))}function Tf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function gc(e,t){Tf(e,t),(e=e.alternate)&&Tf(e,t)}function Ef(e){if(e.tag===13){var t=ha(e,67108864);t!==null&&jt(t,e,67108864),gc(e,67108864)}}var Ps=!0;function Ah(e,t,l,a){var n=N.T;N.T=null;var i=q.p;try{q.p=2,pc(e,t,l,a)}finally{q.p=i,N.T=n}}function Mh(e,t,l,a){var n=N.T;N.T=null;var i=q.p;try{q.p=8,pc(e,t,l,a)}finally{q.p=i,N.T=n}}function pc(e,t,l,a){if(Ps){var n=vc(a);if(n===null)nc(e,t,a,ei,l),_f(e,a);else if(kh(n,e,t,l,a))a.stopPropagation();else if(_f(e,a),t&4&&-1<Ch.indexOf(e)){for(;n!==null;){var i=la(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var r=Ol(i.pendingLanes);if(r!==0){var u=i;for(u.pendingLanes|=2,u.entangledLanes|=2;r;){var f=1<<31-ht(r);u.entanglements[1]|=f,r&=~f}Yt(i),(we&6)===0&&(Hs=Rt()+500,Mn(0))}}break;case 13:u=ha(i,2),u!==null&&jt(u,i,2),Bs(),gc(i,2)}if(i=vc(a),i===null&&nc(e,t,a,ei,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else nc(e,t,a,null,l)}}function vc(e){return e=Si(e),yc(e)}var ei=null;function yc(e){if(ei=null,e=ta(e),e!==null){var t=z(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=p(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ei=e,null}function zf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(gm()){case Bc:return 2;case qc:return 8;case Zn:case pm:return 32;case Yc:return 268435456;default:return 32}default:return 32}}var bc=!1,zl=null,_l=null,Al=null,Ln=new Map,Bn=new Map,Ml=[],Ch="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function _f(e,t){switch(e){case"focusin":case"focusout":zl=null;break;case"dragenter":case"dragleave":_l=null;break;case"mouseover":case"mouseout":Al=null;break;case"pointerover":case"pointerout":Ln.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Bn.delete(t.pointerId)}}function qn(e,t,l,a,n,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},t!==null&&(t=la(t),t!==null&&Ef(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function kh(e,t,l,a,n){switch(t){case"focusin":return zl=qn(zl,e,t,l,a,n),!0;case"dragenter":return _l=qn(_l,e,t,l,a,n),!0;case"mouseover":return Al=qn(Al,e,t,l,a,n),!0;case"pointerover":var i=n.pointerId;return Ln.set(i,qn(Ln.get(i)||null,e,t,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,Bn.set(i,qn(Bn.get(i)||null,e,t,l,a,n)),!0}return!1}function Af(e){var t=ta(e.target);if(t!==null){var l=z(t);if(l!==null){if(t=l.tag,t===13){if(t=p(l),t!==null){e.blockedOn=t,Tm(e.priority,function(){if(l.tag===13){var a=bt();a=fi(a);var n=ha(l,a);n!==null&&jt(n,l,a),gc(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ti(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=vc(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);Ni=a,l.target.dispatchEvent(a),Ni=null}else return t=la(l),t!==null&&Ef(t),e.blockedOn=l,!1;t.shift()}return!0}function Mf(e,t,l){ti(e)&&l.delete(t)}function Oh(){bc=!1,zl!==null&&ti(zl)&&(zl=null),_l!==null&&ti(_l)&&(_l=null),Al!==null&&ti(Al)&&(Al=null),Ln.forEach(Mf),Bn.forEach(Mf)}function li(e,t){e.blockedOn===t&&(e.blockedOn=null,bc||(bc=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Oh)))}var ai=null;function Cf(e){ai!==e&&(ai=e,o.unstable_scheduleCallback(o.unstable_NormalPriority,function(){ai===e&&(ai=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(yc(a||l)===null)continue;break}var i=la(l);i!==null&&(e.splice(t,3),t-=3,yr(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Yn(e){function t(f){return li(f,e)}zl!==null&&li(zl,e),_l!==null&&li(_l,e),Al!==null&&li(Al,e),Ln.forEach(t),Bn.forEach(t);for(var l=0;l<Ml.length;l++){var a=Ml[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Ml.length&&(l=Ml[0],l.blockedOn===null);)Af(l),l.blockedOn===null&&Ml.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],r=n[st]||null;if(typeof i=="function")r||Cf(l);else if(r){var u=null;if(i&&i.hasAttribute("formAction")){if(n=i,r=i[st]||null)u=r.formAction;else if(yc(n)!==null)continue}else u=r.action;typeof u=="function"?l[a+1]=u:(l.splice(a,3),a-=3),Cf(l)}}}function jc(e){this._internalRoot=e}ni.prototype.render=jc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var l=t.current,a=bt();wf(l,a,e,t,null,null)},ni.prototype.unmount=jc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wf(e.current,2,null,e,null,null),Bs(),t[ea]=null}};function ni(e){this._internalRoot=e}ni.prototype.unstable_scheduleHydration=function(e){if(e){var t=Zc();e={blockedOn:null,target:e,priority:t};for(var l=0;l<Ml.length&&t!==0&&t<Ml[l].priority;l++);Ml.splice(l,0,e),l===0&&Af(e)}};var kf=h.version;if(kf!=="19.1.0")throw Error(c(527,kf,"19.1.0"));q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=T(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var Dh={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var si=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!si.isDisabled&&si.supportsFiber)try{Xa=si.inject(Dh),mt=si}catch{}}return Qn.createRoot=function(e,t){if(!y(e))throw Error(c(299));var l=!1,a="",n=Ko,i=Jo,r=$o,u=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(r=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(u=t.unstable_transitionCallbacks)),t=Nf(e,1,!1,null,null,l,a,n,i,r,u,null),e[ea]=t.current,ac(e),new jc(t)},Qn.hydrateRoot=function(e,t,l){if(!y(e))throw Error(c(299));var a=!1,n="",i=Ko,r=Jo,u=$o,f=null,j=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(r=l.onCaughtError),l.onRecoverableError!==void 0&&(u=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(f=l.unstable_transitionCallbacks),l.formState!==void 0&&(j=l.formState)),t=Nf(e,1,!0,t,l??null,a,n,i,r,u,f,j),t.context=Sf(null),l=t.current,a=bt(),a=fi(a),n=ml(a),n.callback=null,hl(l,n,a),l=a,t.current.lanes=l,Za(t,l),Yt(t),e[ea]=t.current,ac(e),new ni(t)},Qn.version="19.1.0",Qn}var Gf;function Vh(){if(Gf)return wc.exports;Gf=1;function o(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o)}catch(h){console.error(h)}}return o(),wc.exports=Xh(),wc.exports}var Zh=Vh();const Kh=()=>{const o="https://hearty-endurance-production.up.railway.app";return console.log("Using API URL from environment:",o),o},et=Kh();console.log("API Configuration:",{baseUrl:et,environment:"production",isProduction:!0,envApiUrl:"https://hearty-endurance-production.up.railway.app"});const Jh=o=>{const h=o.startsWith("/")?o.slice(1):o;return`${et}/${h}`},$f=Z.createContext(void 0),Ya=()=>{const o=Z.useContext($f);if(o===void 0)throw new Error("useAuth must be used within an AuthProvider");return o},$h=({children:o})=>{const[h,d]=Z.useState(null),[c,y]=Z.useState(!0);Z.useEffect(()=>{const g=localStorage.getItem("user"),S=localStorage.getItem("token");g&&S?d(JSON.parse(g)):(localStorage.removeItem("user"),localStorage.removeItem("token")),y(!1)},[]);const T={user:h,login:async(g,S)=>{y(!0);try{const M=await fetch(`${et}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:g,password:S})});if(!M.ok){const H=await M.json().catch(()=>({}));return console.error("Login failed:",H),y(!1),{success:!1,errors:H.errors||[{message:H.message||"Login failed"}]}}const U=await M.json();if(!U.success||!U.data)return console.error("Invalid response format:",U),y(!1),{success:!1,errors:[{message:"Invalid response from server"}]};const O={id:U.data.user.id,name:U.data.user.name,email:U.data.user.email,departmentId:U.data.user.department_id};return d(O),localStorage.setItem("user",JSON.stringify(O)),localStorage.setItem("token",U.data.token),y(!1),{success:!0}}catch(M){return console.error("Login error:",M),y(!1),{success:!1,errors:[{message:"An error occurred. Please try again."}]}}},signup:async(g,S,M,U)=>{y(!0);try{const O=await fetch(`${et}/api/auth/signup`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:g,email:S,password:M,department_id:U})});if(!O.ok){const G=await O.json().catch(()=>({}));return console.error("Signup failed:",G),y(!1),{success:!1,errors:G.errors||[{message:G.message||"Signup failed"}]}}const H=await O.json();if(!H.success||!H.data)return console.error("Invalid response format:",H),y(!1),{success:!1,errors:[{message:"Invalid response from server"}]};const X={id:H.data.user.id,name:H.data.user.name,email:H.data.user.email,departmentId:H.data.user.department_id};return d(X),localStorage.setItem("user",JSON.stringify(X)),localStorage.setItem("token",H.data.token),y(!1),{success:!0}}catch(O){return console.error("Signup error:",O),y(!1),{success:!1,errors:[{message:"An error occurred. Please try again."}]}}},logout:()=>{d(null),localStorage.removeItem("user"),localStorage.removeItem("token")},isLoading:c};return s.jsx($f.Provider,{value:T,children:o})};function Wf(o){var h,d,c="";if(typeof o=="string"||typeof o=="number")c+=o;else if(typeof o=="object")if(Array.isArray(o)){var y=o.length;for(h=0;h<y;h++)o[h]&&(d=Wf(o[h]))&&(c&&(c+=" "),c+=d)}else for(d in o)o[d]&&(c&&(c+=" "),c+=d);return c}function Wh(){for(var o,h,d=0,c="",y=arguments.length;d<y;d++)(o=arguments[d])&&(h=Wf(o))&&(c&&(c+=" "),c+=h);return c}const Rc="-",Fh=o=>{const h=Ph(o),{conflictingClassGroups:d,conflictingClassGroupModifiers:c}=o;return{getClassGroupId:p=>{const R=p.split(Rc);return R[0]===""&&R.length!==1&&R.shift(),Ff(R,h)||Ih(p)},getConflictingClassGroupIds:(p,R)=>{const T=d[p]||[];return R&&c[p]?[...T,...c[p]]:T}}},Ff=(o,h)=>{if(o.length===0)return h.classGroupId;const d=o[0],c=h.nextPart.get(d),y=c?Ff(o.slice(1),c):void 0;if(y)return y;if(h.validators.length===0)return;const z=o.join(Rc);return h.validators.find(({validator:p})=>p(z))?.classGroupId},Qf=/^\[(.+)\]$/,Ih=o=>{if(Qf.test(o)){const h=Qf.exec(o)[1],d=h?.substring(0,h.indexOf(":"));if(d)return"arbitrary.."+d}},Ph=o=>{const{theme:h,classGroups:d}=o,c={nextPart:new Map,validators:[]};for(const y in d)Mc(d[y],c,y,h);return c},Mc=(o,h,d,c)=>{o.forEach(y=>{if(typeof y=="string"){const z=y===""?h:Xf(h,y);z.classGroupId=d;return}if(typeof y=="function"){if(ex(y)){Mc(y(c),h,d,c);return}h.validators.push({validator:y,classGroupId:d});return}Object.entries(y).forEach(([z,p])=>{Mc(p,Xf(h,z),d,c)})})},Xf=(o,h)=>{let d=o;return h.split(Rc).forEach(c=>{d.nextPart.has(c)||d.nextPart.set(c,{nextPart:new Map,validators:[]}),d=d.nextPart.get(c)}),d},ex=o=>o.isThemeGetter,tx=o=>{if(o<1)return{get:()=>{},set:()=>{}};let h=0,d=new Map,c=new Map;const y=(z,p)=>{d.set(z,p),h++,h>o&&(h=0,c=d,d=new Map)};return{get(z){let p=d.get(z);if(p!==void 0)return p;if((p=c.get(z))!==void 0)return y(z,p),p},set(z,p){d.has(z)?d.set(z,p):y(z,p)}}},Cc="!",kc=":",lx=kc.length,ax=o=>{const{prefix:h,experimentalParseClassName:d}=o;let c=y=>{const z=[];let p=0,R=0,T=0,g;for(let H=0;H<y.length;H++){let X=y[H];if(p===0&&R===0){if(X===kc){z.push(y.slice(T,H)),T=H+lx;continue}if(X==="/"){g=H;continue}}X==="["?p++:X==="]"?p--:X==="("?R++:X===")"&&R--}const S=z.length===0?y:y.substring(T),M=nx(S),U=M!==S,O=g&&g>T?g-T:void 0;return{modifiers:z,hasImportantModifier:U,baseClassName:M,maybePostfixModifierPosition:O}};if(h){const y=h+kc,z=c;c=p=>p.startsWith(y)?z(p.substring(y.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:p,maybePostfixModifierPosition:void 0}}if(d){const y=c;c=z=>d({className:z,parseClassName:y})}return c},nx=o=>o.endsWith(Cc)?o.substring(0,o.length-1):o.startsWith(Cc)?o.substring(1):o,sx=o=>{const h=Object.fromEntries(o.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const y=[];let z=[];return c.forEach(p=>{p[0]==="["||h[p]?(y.push(...z.sort(),p),z=[]):z.push(p)}),y.push(...z.sort()),y}},ix=o=>({cache:tx(o.cacheSize),parseClassName:ax(o),sortModifiers:sx(o),...Fh(o)}),rx=/\s+/,cx=(o,h)=>{const{parseClassName:d,getClassGroupId:c,getConflictingClassGroupIds:y,sortModifiers:z}=h,p=[],R=o.trim().split(rx);let T="";for(let g=R.length-1;g>=0;g-=1){const S=R[g],{isExternal:M,modifiers:U,hasImportantModifier:O,baseClassName:H,maybePostfixModifierPosition:X}=d(S);if(M){T=S+(T.length>0?" "+T:T);continue}let G=!!X,le=c(G?H.substring(0,X):H);if(!le){if(!G){T=S+(T.length>0?" "+T:T);continue}if(le=c(H),!le){T=S+(T.length>0?" "+T:T);continue}G=!1}const F=z(U).join(":"),P=O?F+Cc:F,xe=P+le;if(p.includes(xe))continue;p.push(xe);const ee=y(le,G);for(let ye=0;ye<ee.length;++ye){const ge=ee[ye];p.push(P+ge)}T=S+(T.length>0?" "+T:T)}return T};function ux(){let o=0,h,d,c="";for(;o<arguments.length;)(h=arguments[o++])&&(d=If(h))&&(c&&(c+=" "),c+=d);return c}const If=o=>{if(typeof o=="string")return o;let h,d="";for(let c=0;c<o.length;c++)o[c]&&(h=If(o[c]))&&(d&&(d+=" "),d+=h);return d};function ox(o,...h){let d,c,y,z=p;function p(T){const g=h.reduce((S,M)=>M(S),o());return d=ix(g),c=d.cache.get,y=d.cache.set,z=R,R(T)}function R(T){const g=c(T);if(g)return g;const S=cx(T,d);return y(T,S),S}return function(){return z(ux.apply(null,arguments))}}const Ze=o=>{const h=d=>d[o]||[];return h.isThemeGetter=!0,h},Pf=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,em=/^\((?:(\w[\w-]*):)?(.+)\)$/i,dx=/^\d+\/\d+$/,fx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,mx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,hx=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,xx=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,gx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ba=o=>dx.test(o),oe=o=>!!o&&!Number.isNaN(Number(o)),kl=o=>!!o&&Number.isInteger(Number(o)),_c=o=>o.endsWith("%")&&oe(o.slice(0,-1)),al=o=>fx.test(o),px=()=>!0,vx=o=>mx.test(o)&&!hx.test(o),tm=()=>!1,yx=o=>xx.test(o),bx=o=>gx.test(o),jx=o=>!$(o)&&!W(o),Nx=o=>Ga(o,nm,tm),$=o=>Pf.test(o),Fl=o=>Ga(o,sm,vx),Ac=o=>Ga(o,zx,oe),Vf=o=>Ga(o,lm,tm),Sx=o=>Ga(o,am,bx),ii=o=>Ga(o,im,yx),W=o=>em.test(o),Xn=o=>Qa(o,sm),wx=o=>Qa(o,_x),Zf=o=>Qa(o,lm),Tx=o=>Qa(o,nm),Ex=o=>Qa(o,am),ri=o=>Qa(o,im,!0),Ga=(o,h,d)=>{const c=Pf.exec(o);return c?c[1]?h(c[1]):d(c[2]):!1},Qa=(o,h,d=!1)=>{const c=em.exec(o);return c?c[1]?h(c[1]):d:!1},lm=o=>o==="position"||o==="percentage",am=o=>o==="image"||o==="url",nm=o=>o==="length"||o==="size"||o==="bg-size",sm=o=>o==="length",zx=o=>o==="number",_x=o=>o==="family-name",im=o=>o==="shadow",Ax=()=>{const o=Ze("color"),h=Ze("font"),d=Ze("text"),c=Ze("font-weight"),y=Ze("tracking"),z=Ze("leading"),p=Ze("breakpoint"),R=Ze("container"),T=Ze("spacing"),g=Ze("radius"),S=Ze("shadow"),M=Ze("inset-shadow"),U=Ze("text-shadow"),O=Ze("drop-shadow"),H=Ze("blur"),X=Ze("perspective"),G=Ze("aspect"),le=Ze("ease"),F=Ze("animate"),P=()=>["auto","avoid","all","avoid-page","page","left","right","column"],xe=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],ee=()=>[...xe(),W,$],ye=()=>["auto","hidden","clip","visible","scroll"],ge=()=>["auto","contain","none"],V=()=>[W,$,T],Ce=()=>[Ba,"full","auto",...V()],Be=()=>[kl,"none","subgrid",W,$],ie=()=>["auto",{span:["full",kl,W,$]},kl,W,$],Y=()=>[kl,"auto",W,$],Ne=()=>["auto","min","max","fr",W,$],ke=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],L=()=>["start","end","center","stretch","center-safe","end-safe"],N=()=>["auto",...V()],q=()=>[Ba,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...V()],D=()=>[o,W,$],ce=()=>[...xe(),Zf,Vf,{position:[W,$]}],m=()=>["no-repeat",{repeat:["","x","y","space","round"]}],k=()=>["auto","cover","contain",Tx,Nx,{size:[W,$]}],Q=()=>[_c,Xn,Fl],B=()=>["","none","full",g,W,$],K=()=>["",oe,Xn,Fl],fe=()=>["solid","dashed","dotted","double"],se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],be=()=>[oe,_c,Zf,Vf],Ae=()=>["","none",H,W,$],ft=()=>["none",oe,W,$],sl=()=>["none",oe,W,$],il=()=>[oe,W,$],rl=()=>[Ba,"full",...V()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[al],breakpoint:[al],color:[px],container:[al],"drop-shadow":[al],ease:["in","out","in-out"],font:[jx],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[al],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[al],shadow:[al],spacing:["px",oe],text:[al],"text-shadow":[al],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ba,$,W,G]}],container:["container"],columns:[{columns:[oe,$,W,R]}],"break-after":[{"break-after":P()}],"break-before":[{"break-before":P()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:ee()}],overflow:[{overflow:ye()}],"overflow-x":[{"overflow-x":ye()}],"overflow-y":[{"overflow-y":ye()}],overscroll:[{overscroll:ge()}],"overscroll-x":[{"overscroll-x":ge()}],"overscroll-y":[{"overscroll-y":ge()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Ce()}],"inset-x":[{"inset-x":Ce()}],"inset-y":[{"inset-y":Ce()}],start:[{start:Ce()}],end:[{end:Ce()}],top:[{top:Ce()}],right:[{right:Ce()}],bottom:[{bottom:Ce()}],left:[{left:Ce()}],visibility:["visible","invisible","collapse"],z:[{z:[kl,"auto",W,$]}],basis:[{basis:[Ba,"full","auto",R,...V()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[oe,Ba,"auto","initial","none",$]}],grow:[{grow:["",oe,W,$]}],shrink:[{shrink:["",oe,W,$]}],order:[{order:[kl,"first","last","none",W,$]}],"grid-cols":[{"grid-cols":Be()}],"col-start-end":[{col:ie()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":Be()}],"row-start-end":[{row:ie()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Ne()}],"auto-rows":[{"auto-rows":Ne()}],gap:[{gap:V()}],"gap-x":[{"gap-x":V()}],"gap-y":[{"gap-y":V()}],"justify-content":[{justify:[...ke(),"normal"]}],"justify-items":[{"justify-items":[...L(),"normal"]}],"justify-self":[{"justify-self":["auto",...L()]}],"align-content":[{content:["normal",...ke()]}],"align-items":[{items:[...L(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...L(),{baseline:["","last"]}]}],"place-content":[{"place-content":ke()}],"place-items":[{"place-items":[...L(),"baseline"]}],"place-self":[{"place-self":["auto",...L()]}],p:[{p:V()}],px:[{px:V()}],py:[{py:V()}],ps:[{ps:V()}],pe:[{pe:V()}],pt:[{pt:V()}],pr:[{pr:V()}],pb:[{pb:V()}],pl:[{pl:V()}],m:[{m:N()}],mx:[{mx:N()}],my:[{my:N()}],ms:[{ms:N()}],me:[{me:N()}],mt:[{mt:N()}],mr:[{mr:N()}],mb:[{mb:N()}],ml:[{ml:N()}],"space-x":[{"space-x":V()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":V()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[R,"screen",...q()]}],"min-w":[{"min-w":[R,"screen","none",...q()]}],"max-w":[{"max-w":[R,"screen","none","prose",{screen:[p]},...q()]}],h:[{h:["screen","lh",...q()]}],"min-h":[{"min-h":["screen","lh","none",...q()]}],"max-h":[{"max-h":["screen","lh",...q()]}],"font-size":[{text:["base",d,Xn,Fl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,W,Ac]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",_c,$]}],"font-family":[{font:[wx,$,h]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[y,W,$]}],"line-clamp":[{"line-clamp":[oe,"none",W,Ac]}],leading:[{leading:[z,...V()]}],"list-image":[{"list-image":["none",W,$]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",W,$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...fe(),"wavy"]}],"text-decoration-thickness":[{decoration:[oe,"from-font","auto",W,Fl]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[oe,"auto",W,$]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W,$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W,$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ce()}],"bg-repeat":[{bg:m()}],"bg-size":[{bg:k()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},kl,W,$],radial:["",W,$],conic:[kl,W,$]},Ex,Sx]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:Q()}],"gradient-via-pos":[{via:Q()}],"gradient-to-pos":[{to:Q()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:B()}],"rounded-s":[{"rounded-s":B()}],"rounded-e":[{"rounded-e":B()}],"rounded-t":[{"rounded-t":B()}],"rounded-r":[{"rounded-r":B()}],"rounded-b":[{"rounded-b":B()}],"rounded-l":[{"rounded-l":B()}],"rounded-ss":[{"rounded-ss":B()}],"rounded-se":[{"rounded-se":B()}],"rounded-ee":[{"rounded-ee":B()}],"rounded-es":[{"rounded-es":B()}],"rounded-tl":[{"rounded-tl":B()}],"rounded-tr":[{"rounded-tr":B()}],"rounded-br":[{"rounded-br":B()}],"rounded-bl":[{"rounded-bl":B()}],"border-w":[{border:K()}],"border-w-x":[{"border-x":K()}],"border-w-y":[{"border-y":K()}],"border-w-s":[{"border-s":K()}],"border-w-e":[{"border-e":K()}],"border-w-t":[{"border-t":K()}],"border-w-r":[{"border-r":K()}],"border-w-b":[{"border-b":K()}],"border-w-l":[{"border-l":K()}],"divide-x":[{"divide-x":K()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":K()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...fe(),"hidden","none"]}],"divide-style":[{divide:[...fe(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[...fe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[oe,W,$]}],"outline-w":[{outline:["",oe,Xn,Fl]}],"outline-color":[{outline:D()}],shadow:[{shadow:["","none",S,ri,ii]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",M,ri,ii]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:K()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[oe,Fl]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":K()}],"inset-ring-color":[{"inset-ring":D()}],"text-shadow":[{"text-shadow":["none",U,ri,ii]}],"text-shadow-color":[{"text-shadow":D()}],opacity:[{opacity:[oe,W,$]}],"mix-blend":[{"mix-blend":[...se(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":se()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[oe]}],"mask-image-linear-from-pos":[{"mask-linear-from":be()}],"mask-image-linear-to-pos":[{"mask-linear-to":be()}],"mask-image-linear-from-color":[{"mask-linear-from":D()}],"mask-image-linear-to-color":[{"mask-linear-to":D()}],"mask-image-t-from-pos":[{"mask-t-from":be()}],"mask-image-t-to-pos":[{"mask-t-to":be()}],"mask-image-t-from-color":[{"mask-t-from":D()}],"mask-image-t-to-color":[{"mask-t-to":D()}],"mask-image-r-from-pos":[{"mask-r-from":be()}],"mask-image-r-to-pos":[{"mask-r-to":be()}],"mask-image-r-from-color":[{"mask-r-from":D()}],"mask-image-r-to-color":[{"mask-r-to":D()}],"mask-image-b-from-pos":[{"mask-b-from":be()}],"mask-image-b-to-pos":[{"mask-b-to":be()}],"mask-image-b-from-color":[{"mask-b-from":D()}],"mask-image-b-to-color":[{"mask-b-to":D()}],"mask-image-l-from-pos":[{"mask-l-from":be()}],"mask-image-l-to-pos":[{"mask-l-to":be()}],"mask-image-l-from-color":[{"mask-l-from":D()}],"mask-image-l-to-color":[{"mask-l-to":D()}],"mask-image-x-from-pos":[{"mask-x-from":be()}],"mask-image-x-to-pos":[{"mask-x-to":be()}],"mask-image-x-from-color":[{"mask-x-from":D()}],"mask-image-x-to-color":[{"mask-x-to":D()}],"mask-image-y-from-pos":[{"mask-y-from":be()}],"mask-image-y-to-pos":[{"mask-y-to":be()}],"mask-image-y-from-color":[{"mask-y-from":D()}],"mask-image-y-to-color":[{"mask-y-to":D()}],"mask-image-radial":[{"mask-radial":[W,$]}],"mask-image-radial-from-pos":[{"mask-radial-from":be()}],"mask-image-radial-to-pos":[{"mask-radial-to":be()}],"mask-image-radial-from-color":[{"mask-radial-from":D()}],"mask-image-radial-to-color":[{"mask-radial-to":D()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":xe()}],"mask-image-conic-pos":[{"mask-conic":[oe]}],"mask-image-conic-from-pos":[{"mask-conic-from":be()}],"mask-image-conic-to-pos":[{"mask-conic-to":be()}],"mask-image-conic-from-color":[{"mask-conic-from":D()}],"mask-image-conic-to-color":[{"mask-conic-to":D()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ce()}],"mask-repeat":[{mask:m()}],"mask-size":[{mask:k()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",W,$]}],filter:[{filter:["","none",W,$]}],blur:[{blur:Ae()}],brightness:[{brightness:[oe,W,$]}],contrast:[{contrast:[oe,W,$]}],"drop-shadow":[{"drop-shadow":["","none",O,ri,ii]}],"drop-shadow-color":[{"drop-shadow":D()}],grayscale:[{grayscale:["",oe,W,$]}],"hue-rotate":[{"hue-rotate":[oe,W,$]}],invert:[{invert:["",oe,W,$]}],saturate:[{saturate:[oe,W,$]}],sepia:[{sepia:["",oe,W,$]}],"backdrop-filter":[{"backdrop-filter":["","none",W,$]}],"backdrop-blur":[{"backdrop-blur":Ae()}],"backdrop-brightness":[{"backdrop-brightness":[oe,W,$]}],"backdrop-contrast":[{"backdrop-contrast":[oe,W,$]}],"backdrop-grayscale":[{"backdrop-grayscale":["",oe,W,$]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[oe,W,$]}],"backdrop-invert":[{"backdrop-invert":["",oe,W,$]}],"backdrop-opacity":[{"backdrop-opacity":[oe,W,$]}],"backdrop-saturate":[{"backdrop-saturate":[oe,W,$]}],"backdrop-sepia":[{"backdrop-sepia":["",oe,W,$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":V()}],"border-spacing-x":[{"border-spacing-x":V()}],"border-spacing-y":[{"border-spacing-y":V()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",W,$]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[oe,"initial",W,$]}],ease:[{ease:["linear","initial",le,W,$]}],delay:[{delay:[oe,W,$]}],animate:[{animate:["none",F,W,$]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[X,W,$]}],"perspective-origin":[{"perspective-origin":ee()}],rotate:[{rotate:ft()}],"rotate-x":[{"rotate-x":ft()}],"rotate-y":[{"rotate-y":ft()}],"rotate-z":[{"rotate-z":ft()}],scale:[{scale:sl()}],"scale-x":[{"scale-x":sl()}],"scale-y":[{"scale-y":sl()}],"scale-z":[{"scale-z":sl()}],"scale-3d":["scale-3d"],skew:[{skew:il()}],"skew-x":[{"skew-x":il()}],"skew-y":[{"skew-y":il()}],transform:[{transform:[W,$,"","none","gpu","cpu"]}],"transform-origin":[{origin:ee()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:rl()}],"translate-x":[{"translate-x":rl()}],"translate-y":[{"translate-y":rl()}],"translate-z":[{"translate-z":rl()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W,$]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W,$]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[oe,Xn,Fl,Ac]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Mx=ox(Ax);function Le(...o){return Mx(Wh(o))}const pe=dt.forwardRef(({className:o,variant:h="primary",size:d="md",loading:c=!1,icon:y,iconPosition:z="left",children:p,disabled:R,...T},g)=>{const S="btn focus-ring",M={primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",warning:"btn-warning",error:"btn-error",ghost:"btn-ghost"},U={sm:"btn-sm",md:"",lg:"btn-lg"},O=R||c;return s.jsxs("button",{className:Le(S,M[h],U[d],O&&"opacity-50 cursor-not-allowed",o),ref:g,disabled:O,...T,children:[c&&s.jsx("div",{className:"loading-spinner mr-2"}),!c&&y&&z==="left"&&s.jsx("span",{className:"mr-2",children:y}),p,!c&&y&&z==="right"&&s.jsx("span",{className:"ml-2",children:y})]})});pe.displayName="Button";const Dt=dt.forwardRef(({className:o,variant:h="default",padding:d="md",children:c,...y},z)=>{const p="card",R={default:"",interactive:"card-interactive",elevated:"shadow-lg hover:shadow-xl"},T={none:"",sm:"p-4",md:"p-6",lg:"p-8"};return s.jsx("div",{ref:z,className:Le(p,R[h],T[d],o),...y,children:c})});Dt.displayName="Card";const ci=dt.forwardRef(({className:o,...h},d)=>s.jsx("div",{ref:d,className:Le("flex flex-col space-y-1.5 p-6",o),...h}));ci.displayName="CardHeader";const Hc=dt.forwardRef(({className:o,...h},d)=>s.jsx("h3",{ref:d,className:Le("text-lg font-semibold leading-none tracking-tight text-neutral-900",o),...h}));Hc.displayName="CardTitle";const rm=dt.forwardRef(({className:o,...h},d)=>s.jsx("p",{ref:d,className:Le("text-sm text-neutral-600",o),...h}));rm.displayName="CardDescription";const Ut=dt.forwardRef(({className:o,...h},d)=>s.jsx("div",{ref:d,className:Le("p-6 pt-0",o),...h}));Ut.displayName="CardContent";const Cx=dt.forwardRef(({className:o,...h},d)=>s.jsx("div",{ref:d,className:Le("flex items-center p-6 pt-0",o),...h}));Cx.displayName="CardFooter";const Il=dt.forwardRef(({className:o,type:h="text",variant:d="default",label:c,helperText:y,error:z,icon:p,iconPosition:R="left",id:T,...g},S)=>{const M=T||`input-${Math.random().toString(36).substr(2,9)}`,G=Le("input",{default:"",error:"input-error",success:"input-success"}[d==="error"||!!z?"error":d==="success"?"success":"default"],p&&R==="left"&&"pl-10",p&&R==="right"&&"pr-10",o);return s.jsxs("div",{className:"space-y-2",children:[c&&s.jsx("label",{htmlFor:M,className:"block text-sm font-medium text-neutral-700",children:c}),s.jsxs("div",{className:"relative",children:[p&&R==="left"&&s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx("span",{className:"text-neutral-400 text-sm",children:p})}),s.jsx("input",{type:h,className:G,ref:S,id:M,...g}),p&&R==="right"&&s.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:s.jsx("span",{className:"text-neutral-400 text-sm",children:p})})]}),(z||y)&&s.jsx("p",{className:Le("text-sm",z?"text-red-600":"text-neutral-600"),children:z||y})]})});Il.displayName="Input";const ot=dt.forwardRef(({className:o,variant:h="neutral",size:d="md",dot:c=!1,children:y,...z},p)=>{const R="badge",T={primary:"badge-primary",secondary:"badge-secondary",success:"badge-success",warning:"badge-warning",error:"badge-error",neutral:"badge-neutral"},g={sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-0.5 text-xs",lg:"px-3 py-1 text-sm"};return s.jsxs("div",{ref:p,className:Le(R,T[h],g[d],o),...z,children:[c&&s.jsx("span",{className:"w-1.5 h-1.5 rounded-full bg-current mr-1.5"}),y]})});ot.displayName="Badge";const Pl=dt.forwardRef(({className:o,status:h,showText:d=!0,size:c="md",...y},z)=>{const p={active:{label:"Active",dotClass:"bg-success-500 animate-pulse",textClass:"text-success-700"},idle:{label:"Idle",dotClass:"bg-warning-500",textClass:"text-warning-700"},offline:{label:"Offline",dotClass:"bg-neutral-400",textClass:"text-neutral-600"}},R={sm:{dot:"w-1.5 h-1.5",text:"text-xs",spacing:"mr-1.5"},md:{dot:"w-2 h-2",text:"text-sm",spacing:"mr-2"},lg:{dot:"w-2.5 h-2.5",text:"text-base",spacing:"mr-2.5"}},T=h&&p[h]?h:"offline",g=p[T],S=R[c];return s.jsxs("div",{ref:z,className:Le("status-indicator inline-flex items-center",o),...y,children:[s.jsx("span",{className:Le("status-dot rounded-full",S.dot,S.spacing,g.dotClass)}),d&&s.jsx("span",{className:Le(S.text,g.textClass,"font-medium"),children:g.label})]})});Pl.displayName="StatusIndicator";const nl=({isOpen:o,onClose:h,title:d,children:c,size:y="md",closeOnOverlayClick:z=!0,showCloseButton:p=!0})=>{const[R,T]=Z.useState(!1),[g,S]=Z.useState(!1),M={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl"};Z.useEffect(()=>{if(o&&!g)T(!1),S(!0);else if(!o&&g){T(!0);const H=setTimeout(()=>{S(!1),T(!1)},300);return()=>clearTimeout(H)}},[o,g]),Z.useEffect(()=>{const H=X=>{X.key==="Escape"&&o&&!R&&U()};return g&&(document.addEventListener("keydown",H),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",H),document.body.style.overflow="unset"}},[g,o,R]);const U=()=>{R||h()},O=H=>{H.target===H.currentTarget&&z&&!R&&U()};return g?s.jsx("div",{className:Le("modal-overlay",R?"animate-fade-out":"animate-fade-in"),onClick:O,children:s.jsxs("div",{className:Le("modal-content",M[y],R?"animate-modal-exit":"animate-modal-enter"),children:[(d||p)&&s.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-neutral-200 flex-shrink-0",children:[d&&s.jsx("h2",{className:"text-lg font-semibold text-neutral-900",children:d}),p&&s.jsx("button",{onClick:U,className:"text-neutral-400 hover:text-neutral-600 transition-colors duration-200 focus-ring rounded-lg p-1","aria-label":"Close modal",children:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s.jsx("div",{className:"p-6 flex-1 overflow-y-auto",children:c})]})}):null},kx=dt.forwardRef(({className:o,size:h="md",variant:d="primary",...c},y)=>{const z={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"},p={primary:"border-primary-600 border-t-transparent",secondary:"border-secondary-600 border-t-transparent",white:"border-white border-t-transparent"};return s.jsx("div",{ref:y,className:Le("loading-spinner border-2 rounded-full animate-spin",z[h],p[d],o),...c})});kx.displayName="LoadingSpinner";const Ox=dt.forwardRef(({className:o,variant:h="rectangular",width:d,height:c,lines:y=1,...z},p)=>{const R="loading-skeleton",T={text:"h-4 rounded",circular:"rounded-full",rectangular:"rounded-lg"},g={width:d||(h==="circular"?c:"100%"),height:c||(h==="text"?"1rem":"4rem")};return h==="text"&&y>1?s.jsx("div",{ref:p,className:Le("space-y-2",o),...z,children:Array.from({length:y}).map((S,M)=>s.jsx("div",{className:Le(R,T[h]),style:{...g,width:M===y-1?"75%":g.width}},M))}):s.jsx("div",{ref:p,className:Le(R,T[h],o),style:g,...z})});Ox.displayName="SkeletonLoader";const Lc=dt.forwardRef(({className:o,icon:h,title:d,description:c,action:y,...z},p)=>{const R=s.jsx("svg",{className:"w-12 h-12 text-neutral-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"})});return s.jsxs("div",{ref:p,className:Le("text-center py-12 px-6",o),...z,children:[s.jsx("div",{className:"mx-auto mb-4",children:h||R}),s.jsx("h3",{className:"text-lg font-semibold text-neutral-900 mb-2",children:d}),c&&s.jsx("p",{className:"text-neutral-600 mb-6 max-w-sm mx-auto",children:c}),y&&s.jsx(pe,{variant:y.variant||"primary",onClick:y.onClick,children:y.label})]})});Lc.displayName="EmptyState";class Dx extends Z.Component{state={hasError:!1};static getDerivedStateFromError(h){return{hasError:!0,error:h}}componentDidCatch(h,d){console.error("Uncaught error:",h,d)}handleRetry=()=>{this.setState({hasError:!1,error:void 0})};render(){return this.state.hasError?this.props.fallback?this.props.fallback:s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-neutral-50 p-4",children:s.jsx(Dt,{className:"max-w-md w-full",children:s.jsxs(Ut,{className:"text-center py-8",children:[s.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsx("h2",{className:"text-xl font-bold text-neutral-900 mb-2",children:"Something went wrong"}),s.jsx("p",{className:"text-neutral-600 mb-6",children:"We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists."}),s.jsxs("div",{className:"space-y-3",children:[s.jsx(pe,{variant:"primary",onClick:this.handleRetry,className:"w-full",children:"Try Again"}),s.jsx(pe,{variant:"secondary",onClick:()=>window.location.reload(),className:"w-full",children:"Refresh Page"})]}),!1]})})}):this.props.children}}function cm(o){const h=typeof o=="string"?new Date(o):o;return isNaN(h.getTime())?"Invalid Time":h.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})}function Ux(o){const h=typeof o=="string"?new Date(o):o;return isNaN(h.getTime())?"Invalid Time":h.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",second:"2-digit",hour12:!0})}function um(o){const h=typeof o=="string"?new Date(o):o;if(isNaN(h.getTime()))return"Invalid Date";const d=h.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),c=cm(h);return`${d} at ${c}`}function Kf(){return cm(new Date)}function Jf(){return Ux(new Date)}function Rx(o){if(!o)return"";const h=new Date(o);return um(h)}const om=dt.forwardRef(({className:o,showSeconds:h=!1,size:d="md",variant:c="default",...y},z)=>{const[p,R]=Z.useState("");Z.useEffect(()=>{const S=()=>{const U=h?Jf():Kf();R(U)};S();const M=setInterval(S,h?1e3:3e4);return()=>clearInterval(M)},[h]);const T={sm:"text-sm",md:"text-base",lg:"text-lg"},g={default:"text-neutral-700 font-medium",minimal:"text-neutral-600 font-normal",badge:"text-neutral-700 font-semibold"};return s.jsxs("div",{ref:z,className:Le("clock inline-flex items-center",T[d],g[c],o),title:`Current time: ${p}`,...y,children:[s.jsx("svg",{className:"w-5 h-5 mr-2 text-neutral-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsx("span",{className:"tabular-nums",children:p||(h?Jf():Kf())})]})});om.displayName="Clock";const dm=({employeeId:o,currentTask:h,initialData:d,onSubmit:c,onCancel:y})=>{const{user:z}=Ya();if(!z||z.id!==o)return s.jsx(nl,{isOpen:!0,onClose:y,title:"Access Denied",size:"sm",children:s.jsxs("div",{className:"text-center py-4",children:[s.jsx("p",{className:"text-red-600 mb-4",children:"You can only update your own tasks."}),s.jsx(pe,{variant:"secondary",onClick:y,children:"Close"})]})});const[p,R]=Z.useState({task:d?.task||h,status:"active",priority:d?.priority||"medium",taskCategory:d?.taskCategory||"question-creation",progressPercentage:d?.progressPercentage||0,relatedProject:d?.relatedProject||"",blockingIssues:d?.blockingIssues||"",tags:d?.tags||[],numberOfQuestions:d?.numberOfQuestions||1,expectedFinishDateTime:d?.expectedFinishDateTime||""}),[T,g]=Z.useState({}),S=()=>{const O={};return p.task.trim()?p.task.trim().length<5&&(O.task="Task description must be at least 5 characters"):O.task="Task description is required",(p.numberOfQuestions<1||p.numberOfQuestions>1e3)&&(O.numberOfQuestions="Number of questions must be between 1 and 1000"),p.expectedFinishDateTime&&new Date(p.expectedFinishDateTime)<=new Date&&(O.expectedFinishDateTime="Expected finish time must be in the future"),g(O),Object.keys(O).length===0},M=O=>{O.preventDefault(),S()&&c({...p,task:p.task.trim()})},U=(O,H)=>{R(X=>({...X,[O]:H})),T[O]&&g(X=>({...X,[O]:void 0}))};return s.jsx(nl,{isOpen:!0,onClose:y,title:"Update Your Status",size:"3xl",children:s.jsxs("form",{onSubmit:M,className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"task",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Current Task *"}),s.jsx("textarea",{id:"task",value:p.task,onChange:O=>U("task",O.target.value),rows:3,className:"input resize-none",placeholder:"Describe what you're currently working on..."}),T.task&&s.jsxs("p",{className:"mt-2 text-sm text-red-600 flex items-center",children:[s.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),T.task]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"priority",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Priority Level"}),s.jsxs("select",{id:"priority",value:p.priority,onChange:O=>U("priority",O.target.value),className:"input",children:[s.jsx("option",{value:"low",children:"🟢 Low - Can wait"}),s.jsx("option",{value:"medium",children:"🟡 Medium - Normal priority"}),s.jsx("option",{value:"high",children:"🟠 High - Important"}),s.jsx("option",{value:"urgent",children:"🔴 Urgent - Critical"})]})]}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"text-green-600 mr-2",children:"🟢"}),s.jsx("span",{className:"text-sm font-medium text-green-800",children:"Status: Active (automatically set when working on a task)"})]})})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.jsxs("div",{children:[s.jsx("label",{htmlFor:"taskCategory",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Task Category"}),s.jsxs("select",{id:"taskCategory",value:p.taskCategory,onChange:O=>U("taskCategory",O.target.value),className:"input",children:[s.jsx("option",{value:"question-creation",children:"❓ Question Creation"}),s.jsx("option",{value:"project-delivery",children:"🚀 Project Delivery"}),s.jsx("option",{value:"uploading",children:"📤 Uploading"}),s.jsx("option",{value:"quality-checking",children:"✅ Quality Checking"})]})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsxs("label",{htmlFor:"progressPercentage",className:"block text-sm font-medium text-neutral-700 mb-2",children:["Progress: ",p.progressPercentage,"%"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("input",{type:"range",id:"progressPercentage",min:"0",max:"100",step:"5",value:p.progressPercentage,onChange:O=>U("progressPercentage",parseInt(O.target.value)),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"}),s.jsx("div",{className:"w-full bg-neutral-200 rounded-full h-2",children:s.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300",style:{width:`${p.progressPercentage}%`}})})]})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"relatedProject",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Related Project/Client"}),s.jsx("input",{type:"text",id:"relatedProject",value:p.relatedProject,onChange:O=>U("relatedProject",O.target.value),className:"input",placeholder:"Enter project or client name (optional)"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"numberOfQuestions",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Number of Questions"}),s.jsx("input",{type:"number",id:"numberOfQuestions",min:"1",max:"1000",value:p.numberOfQuestions,onChange:O=>U("numberOfQuestions",parseInt(O.target.value)||1),className:"input",placeholder:"e.g., 50"}),T.numberOfQuestions&&s.jsxs("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[s.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),T.numberOfQuestions]})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"expectedFinishDateTime",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Expected Finish Time"}),s.jsx("input",{type:"datetime-local",id:"expectedFinishDateTime",value:p.expectedFinishDateTime,onChange:O=>U("expectedFinishDateTime",O.target.value),className:"input",min:new Date(Date.now()+5*6e4).toISOString().slice(0,16)}),p.expectedFinishDateTime&&s.jsxs("p",{className:"mt-1 text-xs text-neutral-600",children:["Selected: ",Rx(p.expectedFinishDateTime)]}),T.expectedFinishDateTime&&s.jsxs("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[s.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),T.expectedFinishDateTime]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"blockingIssues",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Blocking Issues"}),s.jsx("textarea",{id:"blockingIssues",value:p.blockingIssues,onChange:O=>U("blockingIssues",O.target.value),rows:3,className:"input resize-none",placeholder:"Any issues blocking progress..."})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"tags",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Tags"}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("input",{type:"text",id:"tags",placeholder:"Type a tag and press Enter",className:"input",onKeyDown:O=>{if(O.key==="Enter"){O.preventDefault();const H=O.target,X=H.value.trim();if(X&&!p.tags.some(G=>G.label===X)){const G={id:Date.now().toString(),label:X,color:`hsl(${Math.random()*360}, 70%, 50%)`};U("tags",[...p.tags,G]),H.value=""}}}}),p.tags.length>0&&s.jsx("div",{className:"flex flex-wrap gap-2",children:p.tags.map(O=>s.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[O.label,s.jsx("button",{type:"button",onClick:()=>{U("tags",p.tags.filter(H=>H.id!==O.id))},className:"ml-1 text-blue-600 hover:text-blue-800 focus:outline-none",children:"×"})]},O.id))})]})]})]}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsxs("div",{className:"ml-3",children:[s.jsx("h3",{className:"text-sm font-semibold text-blue-800 mb-2",children:"Quick Tips"}),s.jsxs("div",{className:"text-sm text-blue-700 grid grid-cols-1 md:grid-cols-2 gap-2",children:[s.jsxs("p",{children:["• ",s.jsx("strong",{children:"Priority:"})," Help managers prioritize tasks"]}),s.jsxs("p",{children:["• ",s.jsx("strong",{children:"Progress:"})," Track completion percentage"]}),s.jsxs("p",{children:["• ",s.jsx("strong",{children:"Duration:"})," Estimate for workload planning"]}),s.jsxs("p",{children:["• ",s.jsx("strong",{children:"Tags:"})," Add keywords for easy filtering"]})]})]})]})}),s.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-neutral-200",children:[s.jsx(pe,{type:"button",variant:"secondary",onClick:y,className:"px-6",children:"Cancel"}),s.jsx(pe,{type:"submit",variant:"primary",className:"px-6",icon:s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"})}),children:"Update Status"})]})]})})},fm=({isOpen:o,onClose:h,taskData:d,taskId:c,onEdit:y,onKeep:z})=>{const[p,R]=Z.useState(!1),[T,g]=Z.useState(!1),S=async()=>{if(!c){console.error("No task ID provided for discard");return}R(!0);try{const H=localStorage.getItem("token");if(!H){console.error("No authentication token found");return}const X=await fetch(`${et}/api/tasks/${c}`,{method:"DELETE",headers:{Authorization:`Bearer ${H}`,"Content-Type":"application/json"}});if(!X.ok){const le=await X.json().catch(()=>({}));console.error("Failed to discard task:",le);return}const G=await X.json();console.log("Task discarded successfully:",G),h()}catch(H){console.error("Error discarding task:",H)}finally{R(!1),g(!1)}},M=H=>H?new Date(H).toLocaleString():"Not set",U=H=>{switch(H){case"urgent":return"error";case"high":return"warning";case"medium":return"primary";case"low":return"secondary";default:return"secondary"}},O=H=>{switch(H){case"active":return"success";case"idle":return"warning";case"offline":return"neutral";default:return"neutral"}};return T?s.jsx(nl,{isOpen:o,onClose:()=>g(!1),title:"Confirm Discard",size:"md",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-red-900",children:"Are you sure?"}),s.jsx("p",{className:"text-sm text-red-700 mt-1",children:"This will permanently delete your task. This action cannot be undone."})]})]}),s.jsxs("div",{className:"p-3 bg-neutral-50 rounded-lg",children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Task to be deleted:"}),s.jsx("p",{className:"text-sm text-neutral-900",children:d.task})]}),s.jsxs("div",{className:"flex space-x-3 pt-4",children:[s.jsx(pe,{variant:"error",onClick:S,disabled:p,className:"flex-1",children:p?"Discarding...":"Yes, Discard Task"}),s.jsx(pe,{variant:"secondary",onClick:()=>g(!1),disabled:p,className:"flex-1",children:"Cancel"})]})]})}):s.jsx(nl,{isOpen:o,onClose:h,title:"Task Created Successfully!",size:"2xl",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center space-x-3 p-4 bg-green-50 rounded-lg border border-green-200",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-green-900",children:"Your task has been created!"}),s.jsx("p",{className:"text-sm text-green-700 mt-1",children:"You can now edit, discard, or keep this task as is."})]})]}),s.jsxs("div",{className:"bg-neutral-50 rounded-lg p-4 space-y-4",children:[s.jsx("h4",{className:"font-medium text-neutral-900",children:"Task Summary"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Description:"}),s.jsx("p",{className:"text-sm text-neutral-900 bg-white p-3 rounded border",children:d.task})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Status:"}),s.jsx(ot,{variant:O(d.status),size:"sm",children:d.status.charAt(0).toUpperCase()+d.status.slice(1)})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Priority:"}),s.jsx(ot,{variant:U(d.priority),size:"sm",children:d.priority.charAt(0).toUpperCase()+d.priority.slice(1)})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Category:"}),s.jsx("p",{className:"text-sm text-neutral-900",children:d.taskCategory.replace("-"," ").replace(/\b\w/g,H=>H.toUpperCase())})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Expected Finish:"}),s.jsx("p",{className:"text-sm text-neutral-900",children:M(d.expectedFinishDateTime)})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Progress:"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"flex-1 bg-neutral-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${d.progressPercentage}%`}})}),s.jsxs("span",{className:"text-sm text-neutral-900",children:[d.progressPercentage,"%"]})]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Questions:"}),s.jsxs("p",{className:"text-sm text-neutral-900",children:[d.numberOfQuestions||0," questions"]})]})]}),d.relatedProject&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Related Project:"}),s.jsx("p",{className:"text-sm text-neutral-900 bg-blue-50 p-2 rounded border",children:d.relatedProject})]}),d.expectedFinishDateTime&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Expected Completion:"}),s.jsx("p",{className:"text-sm text-neutral-900",children:new Date(d.expectedFinishDateTime).toLocaleString()})]}),d.blockingIssues&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-1",children:"Blocking Issues:"}),s.jsx("p",{className:"text-sm text-neutral-900 bg-red-50 p-2 rounded border text-red-800",children:d.blockingIssues})]}),d.tags.length>0&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Tags:"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:d.tags.map(H=>s.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",style:{backgroundColor:H.color+"20",color:H.color},children:H.label},H.id))})]})]})]}),s.jsxs("div",{className:"flex space-x-3 pt-4 border-t border-neutral-200",children:[s.jsx(pe,{variant:"primary",onClick:z,className:"flex-1",children:"Keep Task"}),s.jsx(pe,{variant:"secondary",onClick:y,className:"flex-1",children:"Edit Task"}),s.jsx(pe,{variant:"error",onClick:()=>g(!0),className:"flex-1",children:"Discard Task"})]})]})})},mm=({isOpen:o,onClose:h,taskData:d})=>{const c=p=>{if(!p)return"Not specified";try{return new Date(p).toLocaleString()}catch{return"Invalid date"}},y=p=>{switch(p){case"urgent":return"error";case"high":return"warning";case"medium":return"primary";case"low":return"secondary";default:return"secondary"}},z=p=>{switch(p){case"completed":return"success";case"active":return"primary";case"paused":return"warning";case"cancelled":return"error";default:return"neutral"}};return s.jsx(nl,{isOpen:o,onClose:h,title:"Task Details",size:"2xl",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Task Description:"}),s.jsx("div",{className:"bg-neutral-50 p-4 rounded-lg border",children:s.jsx("p",{className:"text-sm text-neutral-900 leading-relaxed",children:d.task_description||"No description available"})})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Status:"}),s.jsx(ot,{variant:z(d.status||""),size:"sm",children:d.status?d.status.charAt(0).toUpperCase()+d.status.slice(1):"Unknown"})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Priority:"}),s.jsx(ot,{variant:y(d.priority||""),size:"sm",children:d.priority?d.priority.charAt(0).toUpperCase()+d.priority.slice(1):"Unknown"})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Category:"}),s.jsx("p",{className:"text-sm text-neutral-900",children:d.category?d.category.replace("-"," ").replace(/\b\w/g,p=>p.toUpperCase()):"General"})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Expected Finish:"}),s.jsx("p",{className:"text-sm text-neutral-900",children:c(d.expected_finish_datetime)})]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Progress:"}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"flex-1 bg-neutral-200 rounded-full h-3",children:s.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:`${d.progress_percentage||0}%`}})}),s.jsxs("span",{className:"text-sm font-medium text-neutral-900 min-w-[3rem]",children:[d.progress_percentage||0,"%"]})]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Number of Questions:"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsxs("span",{className:"text-sm text-neutral-900",children:[d.number_of_questions||0," question",(d.number_of_questions||0)!==1?"s":""]})]})]}),d.project_name&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Related Project:"}),s.jsx("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-200",children:s.jsx("p",{className:"text-sm text-blue-900 font-medium",children:d.project_name})})]}),d.expected_finish_datetime&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Expected Completion:"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsx("span",{className:"text-sm text-neutral-900",children:new Date(d.expected_finish_datetime).toLocaleString()})]})]}),d.blocking_issues&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-neutral-700 mb-2",children:"Blocking Issues:"}),s.jsx("div",{className:"bg-red-50 p-3 rounded-lg border border-red-200",children:s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("svg",{className:"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),s.jsx("p",{className:"text-sm text-red-800",children:d.blocking_issues})]})})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-neutral-200",children:[d.created_at&&s.jsxs("div",{children:[s.jsx("p",{className:"text-xs font-medium text-neutral-500 mb-1",children:"Created:"}),s.jsx("p",{className:"text-xs text-neutral-700",children:new Date(d.created_at).toLocaleString()})]}),d.updated_at&&s.jsxs("div",{children:[s.jsx("p",{className:"text-xs font-medium text-neutral-500 mb-1",children:"Last Updated:"}),s.jsx("p",{className:"text-xs text-neutral-700",children:new Date(d.updated_at).toLocaleString()})]})]})]})})},Vn=async o=>{try{const h=localStorage.getItem("token");if(!h)return console.error("No authentication token found"),null;const d=o?`${et}/api/tasks/users/${o}/current`:`${et}/api/tasks/current`;console.log("Fetching current task:",{url:d,userId:o,hasToken:!!h,tokenPreview:h?`${h.substring(0,20)}...`:"None"});const c=await fetch(d,{headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}});if(console.log("Response status:",c.status),!c.ok){console.error("Failed to fetch current task:",c.status);try{const z=await c.json();console.error("Error response data:",z)}catch{console.error("Could not parse error response")}return c.status===401&&(console.error("Authentication failed - clearing stored token"),localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.reload()),null}return await c.json()}catch(h){return console.error("Error fetching current task:",h),null}},qa=o=>o?new Date(o)<=new Date:!1,Oc=(o,h,d)=>o?qa(d)?"Task completed (time expired)":h||"Current task":"No current task",Dc=o=>{if(!o)return null;const h=new Date(o),d=new Date,c=h.getTime()-d.getTime();if(c<=0)return"Expired";const y=Math.floor(c/(1e3*60)),z=Math.floor(y/60),p=Math.floor(z/24);return p>0?`${p} day${p>1?"s":""} remaining`:z>0?`${z} hour${z>1?"s":""} remaining`:`${y} minute${y>1?"s":""} remaining`},Hx=({isOpen:o,onClose:h,user:d,onStatusUpdate:c,onOpenTaskUpdate:y,onViewProfile:z,currentTask:p,isLoadingTask:R,onViewTaskDetails:T,onCancelTask:g})=>{const[S,M]=Z.useState("active"),[U,O]=Z.useState(!1),H=()=>{c&&c(),O(!1),h()},X=le=>{switch(le){case"active":return"success";case"idle":return"warning";case"offline":return"neutral";default:return"neutral"}},G=le=>{switch(le){case"active":return"🟢";case"idle":return"🟡";case"offline":return"⚫";default:return"⚫"}};return s.jsx(nl,{isOpen:o,onClose:h,title:"Profile & Status",size:"lg",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-neutral-50 rounded-lg",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl font-bold",children:d?.name?.charAt(0)?.toUpperCase()||"U"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-lg font-semibold text-neutral-900",children:d?.name||"Unknown User"}),s.jsx("p",{className:"text-sm text-neutral-600",children:d?.email||"No email"}),s.jsxs("div",{className:"flex items-center mt-2",children:[s.jsx(Pl,{status:S,size:"sm"}),s.jsxs(ot,{variant:X(S),size:"sm",className:"ml-2",children:[G(S)," ",S.charAt(0).toUpperCase()+S.slice(1)]})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h4",{className:"text-md font-medium text-neutral-900",children:"Current Status"}),s.jsx(pe,{variant:"secondary",size:"sm",onClick:()=>{y&&y()},children:"Update Task"})]}),U?s.jsxs("div",{className:"space-y-4 p-4 bg-neutral-50 rounded-lg",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"Current Task"}),s.jsxs("div",{className:"p-3 bg-white border border-neutral-200 rounded-md min-h-[80px] text-sm text-neutral-600",children:[R?"Loading...":Oc(!!p,p?.task_description,p?.expected_finish_datetime),p?.expected_finish_datetime&&s.jsx("div",{className:`mt-2 text-xs ${qa(p.expected_finish_datetime)?"text-orange-600":"text-blue-600"}`,children:Dc(p.expected_finish_datetime)})]}),s.jsx("p",{className:"text-xs text-neutral-500 mt-1",children:'Use "Update Task" button to modify your current task'})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"status",className:"block text-sm font-medium text-neutral-700 mb-2",children:"Status"}),s.jsxs("select",{id:"status",value:S,onChange:le=>M(le.target.value),className:"input",children:[s.jsx("option",{value:"active",children:"🟢 Active - Currently working"}),s.jsx("option",{value:"idle",children:"🟡 Idle - Available but not on task"}),s.jsx("option",{value:"offline",children:"⚫ Offline - Away from work"})]})]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx(pe,{variant:"primary",size:"sm",onClick:H,className:"flex-1",children:"Update Status"}),s.jsx(pe,{variant:"secondary",size:"sm",onClick:()=>O(!1),className:"flex-1",children:"Cancel"})]})]}):s.jsxs("div",{className:"p-4 bg-neutral-50 rounded-lg",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-neutral-600",children:"Current Task:"}),s.jsx("p",{className:`font-medium ${qa(p?.expected_finish_datetime)?"text-orange-600":"text-neutral-900"}`,children:R?"Loading...":Oc(!!p,p?.task_description,p?.expected_finish_datetime)}),p?.expected_finish_datetime&&s.jsx("p",{className:`text-xs mt-1 ${qa(p.expected_finish_datetime)?"text-orange-600":"text-blue-600"}`,children:Dc(p.expected_finish_datetime)})]}),s.jsxs("div",{className:"text-right",children:[s.jsx("p",{className:"text-sm text-neutral-600",children:"Status:"}),s.jsxs("div",{className:"flex items-center justify-end mt-1",children:[s.jsx(Pl,{status:S,size:"sm"}),s.jsx("span",{className:"ml-2 text-sm font-medium text-neutral-900",children:S.charAt(0).toUpperCase()+S.slice(1)})]})]})]}),p&&s.jsxs("div",{className:"flex space-x-2 pt-3 border-t border-neutral-200",children:[s.jsx(pe,{variant:"secondary",size:"sm",onClick:T,className:"flex-1",children:"View Details"}),s.jsx(pe,{variant:"primary",size:"sm",onClick:()=>O(!0),className:"flex-1",children:"Update"}),s.jsx(pe,{variant:"error",size:"sm",onClick:g,className:"flex-1",children:"Cancel"})]})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"text-md font-medium text-neutral-900",children:"Quick Actions"}),s.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[s.jsxs(pe,{variant:"primary",size:"sm",onClick:()=>{y&&y()},className:"flex flex-col items-center p-3 h-auto",children:[s.jsx("span",{className:"text-lg mb-1",children:"✏️"}),s.jsx("span",{className:"text-xs",children:"Update Task"})]}),z&&s.jsxs(pe,{variant:"secondary",size:"sm",onClick:()=>{h(),z&&z()},className:"flex flex-col items-center p-3 h-auto",children:[s.jsx("span",{className:"text-lg mb-1",children:"👤"}),s.jsx("span",{className:"text-xs",children:"Full Profile"})]})]})]})]})})},Lx=({className:o,onStatusUpdate:h,onViewProfile:d})=>{const{user:c,logout:y}=Ya(),[z,p]=Z.useState(!1),[R,T]=Z.useState(!1),[g,S]=Z.useState({isOpen:!1}),[M,U]=Z.useState(null),[O,H]=Z.useState(!1),[X,G]=Z.useState(!1);Z.useEffect(()=>{(async()=>{if(c){H(!0);try{const Y=await Vn();U(Y?.data||null)}catch(Y){console.error("Error loading current task:",Y)}finally{H(!1)}}})()},[c]);const le=()=>{h&&h()},F=()=>{p(!1),T(!0)},P=()=>{M&&G(!0)},xe=async()=>{if(M?.id&&confirm("Are you sure you want to cancel this task? This action cannot be undone."))try{const ie=localStorage.getItem("token"),Y=await fetch(`${et}/api/task-updates/${M.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${ie}`,"Content-Type":"application/json"}});if(!Y.ok)throw new Error(`Failed to cancel task: ${Y.status}`);const Ne=await Vn();U(Ne?.data||null),h&&h(),p(!1)}catch(ie){console.error("Error canceling task:",ie),alert("Failed to cancel task. Please try again.")}},ee=async ie=>{try{const Y=localStorage.getItem("token");if(!Y){console.error("No authentication token found");return}const Ne={task_description:ie.task,status:ie.status,priority:ie.priority,category:ie.taskCategory,progress_percentage:ie.progressPercentage,tags:ie.tags.map(N=>N.label)};ie.relatedProject&&ie.relatedProject.trim()&&(Ne.project_name=ie.relatedProject.trim()),ie.expectedFinishDateTime&&(Ne.expected_completion_date=ie.expectedFinishDateTime),ie.blockingIssues&&(Ne.blocking_issues=ie.blockingIssues),ie.numberOfQuestions&&(Ne.number_of_questions=ie.numberOfQuestions),console.log("Submitting task update:",Ne);const ke=await fetch(`${et}/api/tasks/update`,{method:"POST",headers:{Authorization:`Bearer ${Y}`,"Content-Type":"application/json"},body:JSON.stringify(Ne)});if(!ke.ok){const N=await ke.text();if(console.error("Task update failed:",N),ke.status===401){console.error("Authentication failed - clearing stored token"),localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.reload();return}throw new Error(`HTTP error! status: ${ke.status} - ${N}`)}const L=await ke.json();console.log("Task updated successfully:",L),S({isOpen:!0,taskData:ie,taskId:L.data?.id||L.data?.task_id}),T(!1)}catch(Y){console.error("Error updating task:",Y)}},ye=()=>{T(!1)},ge=async()=>{S({isOpen:!1});try{const ie=await Vn();U(ie?.data||null)}catch(ie){console.error("Error refreshing current task:",ie)}h&&h()},V=()=>{S({isOpen:!1}),T(!0)},Ce=()=>{ge()},Be=()=>{c&&d&&d(c.id)};return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:Le("floating-header animate-float",o),children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(om,{size:"md",variant:"default",className:"text-neutral-700",showSeconds:!1}),s.jsx("div",{className:"w-px h-6 bg-neutral-300"}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(pe,{variant:"ghost",size:"sm",onClick:()=>p(!0),className:"rounded-full p-2 hover:bg-neutral-100",title:`Profile: ${c?.name||"User"}`,"aria-label":`Open profile for ${c?.name||"User"}`,icon:s.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:c?.name?.charAt(0)?.toUpperCase()||"U"})}),s.jsx("span",{className:"text-sm font-medium text-neutral-700 hidden sm:block",children:c?.name||"User"})]}),s.jsx("div",{className:"w-px h-6 bg-neutral-300"}),s.jsx("div",{className:"flex items-center",children:s.jsx(pe,{variant:"primary",size:"sm",onClick:()=>F(),className:"rounded-full px-3 py-2",title:"Update My Task",children:"✏️ Update Task"})}),s.jsx("div",{className:"w-px h-6 bg-neutral-300"}),s.jsx(pe,{variant:"error",size:"sm",onClick:y,className:"rounded-full px-3 py-2 bg-red-600 hover:bg-red-700 text-white",title:"Sign Out","aria-label":"Sign out of your account",icon:s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),children:"Sign Out"})]})}),s.jsx(Hx,{isOpen:z,onClose:()=>p(!1),user:c,onStatusUpdate:le,onOpenTaskUpdate:F,onViewProfile:Be,currentTask:M,isLoadingTask:O,onViewTaskDetails:P,onCancelTask:xe}),R&&c&&s.jsx(dm,{employeeId:c.id,currentTask:"",onSubmit:ee,onCancel:ye}),g.isOpen&&g.taskData&&s.jsx(fm,{isOpen:g.isOpen,onClose:ge,taskData:g.taskData,taskId:g.taskId,onEdit:V,onKeep:Ce}),X&&M&&s.jsx(mm,{isOpen:X,onClose:()=>G(!1),taskData:M})]})},Bx=()=>{const[o,h]=Z.useState(!0),[d,c]=Z.useState({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",departmentId:""}),[y,z]=Z.useState({}),[p,R]=Z.useState([]),[T,g]=Z.useState(!1),{login:S,signup:M,isLoading:U}=Ya();Z.useEffect(()=>{(async()=>{if(!o){g(!0);try{const le=Jh("api/auth/departments");console.log("Fetching departments from:",le);const F=await fetch(le);if(F.ok){const P=await F.json();P.success&&P.data&&(R(P.data),P.data.length>0&&!d.departmentId&&c(xe=>({...xe,departmentId:P.data[0].id})))}else console.error("Failed to fetch departments:",F.statusText)}catch(le){console.error("Error fetching departments:",le)}finally{g(!1)}}})()},[o,d.departmentId]);const O=()=>{const G={};return d.email?/\S+@\S+\.\S+/.test(d.email)?d.email.toLowerCase().endsWith("@imocha.io")||(G.email="Only @imocha.io email addresses are allowed"):G.email="Email is invalid":G.email="Email is required",d.password?o?d.password.length<1&&(G.password="Password is required"):d.password.length<8?G.password="Password must be at least 8 characters long":d.password.length>128?G.password="Password must be less than 128 characters long":/[a-z]/.test(d.password)?/[A-Z]/.test(d.password)?/\d/.test(d.password)?/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(d.password)?["password","password123","123456","123456789","qwerty","abc123","password1","admin","letmein","welcome"].includes(d.password.toLowerCase())&&(G.password="Password is too common and easily guessable"):G.password="Password must contain at least one special character":G.password="Password must contain at least one number":G.password="Password must contain at least one uppercase letter":G.password="Password must contain at least one lowercase letter":G.password="Password is required",o||(d.firstName||(G.firstName="First name is required"),d.lastName||(G.lastName="Last name is required"),d.departmentId||(G.departmentId="Department is required"),d.password!==d.confirmPassword&&(G.confirmPassword="Passwords do not match")),z(G),Object.keys(G).length===0},H=async G=>{if(G.preventDefault(),!!O())try{let le;if(o?le=await S(d.email,d.password):le=await M(`${d.firstName} ${d.lastName}`,d.email,d.password,d.departmentId),!le.success){const F={};le.errors?le.errors.forEach(P=>{if(P.field){const ee={name:"firstName",email:"email",password:"password",department_id:"departmentId"}[P.field]||P.field;ee in F?F[ee]=P.message:F.general=P.message}else F.general=P.message}):F.general="Authentication failed. Please try again.",z(F)}}catch{z({general:"An error occurred. Please try again."})}},X=G=>{const{name:le,value:F}=G.target;c(P=>({...P,[le]:F})),y[le]&&z(P=>({...P,[le]:void 0}))};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-neutral-50 via-indigo-50 to-emerald-50 py-4 px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"max-w-md w-full max-h-screen overflow-hidden",children:s.jsxs(Dt,{className:"shadow-2xl border-0 backdrop-blur-sm bg-white/95",children:[s.jsxs(ci,{className:"text-center pt-2 pb-2",children:[s.jsx(Hc,{className:"text-xl font-bold text-neutral-900 mb-1",children:"Employee Dashboard"}),s.jsx("p",{className:"text-xs text-neutral-600",children:o?"Welcome back! Sign in to your account":"Join our team and create your account"})]}),s.jsx(Ut,{className:"pt-2",children:s.jsxs("form",{className:"space-y-3",onSubmit:H,children:[s.jsxs("div",{className:"space-y-3",children:[!o&&s.jsxs(s.Fragment,{children:[s.jsx(Il,{id:"firstName",name:"firstName",type:"text",label:"First Name",value:d.firstName,onChange:X,placeholder:"Enter your first name",error:y.firstName,icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),s.jsx(Il,{id:"lastName",name:"lastName",type:"text",label:"Last Name",value:d.lastName,onChange:X,placeholder:"Enter your last name",error:y.lastName,icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]}),s.jsxs("div",{className:"space-y-1",children:[s.jsx(Il,{id:"email",name:"email",type:"email",label:"Email Address",autoComplete:"email",value:d.email,onChange:X,placeholder:o?"Enter your email address":"Enter your @imocha.io email",error:y.email,icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"})})}),!o&&s.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Only @imocha.io email addresses are allowed for signup"})]}),s.jsxs("div",{className:"space-y-1",children:[s.jsx(Il,{id:"password",name:"password",type:"password",label:"Password",autoComplete:o?"current-password":"new-password",value:d.password,onChange:X,placeholder:"Enter your password",error:y.password,icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),!o&&s.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 8 characters with uppercase, lowercase, number, and special character"})]}),!o&&s.jsxs(s.Fragment,{children:[s.jsx(Il,{id:"confirmPassword",name:"confirmPassword",type:"password",label:"Confirm Password",autoComplete:"new-password",value:d.confirmPassword,onChange:X,placeholder:"Confirm your password",error:y.confirmPassword,icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"departmentId",className:"block text-sm font-medium text-neutral-700",children:"Department"}),s.jsx("select",{id:"departmentId",name:"departmentId",value:d.departmentId,onChange:X,className:"input",disabled:T||p.length===0,children:T?s.jsx("option",{value:"",children:"Loading departments..."}):p.length===0?s.jsx("option",{value:"",children:"No departments available"}):s.jsxs(s.Fragment,{children:[s.jsx("option",{value:"",children:"Select a department"}),p.map(G=>s.jsx("option",{value:G.id,children:G.name},G.id))]})}),y.departmentId&&s.jsx("p",{className:"text-sm text-red-600",children:y.departmentId}),T&&s.jsx("p",{className:"text-sm text-blue-600",children:"Loading available departments..."}),!T&&p.length===0&&s.jsx("p",{className:"text-sm text-amber-600",children:"No departments are available for signup. Contact your administrator."})]})]})]}),y.general&&s.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:s.jsx("p",{className:"text-sm text-red-600 text-center",children:y.general})}),s.jsxs("div",{className:"space-y-4",children:[s.jsx(pe,{type:"submit",variant:"primary",size:"lg",loading:U,className:"w-full",children:o?"Sign In":"Create Account"}),s.jsx("div",{className:"text-center",children:s.jsx(pe,{type:"button",variant:"ghost",onClick:()=>{h(!o),z({}),c({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",departmentId:p.length>0?p[0].id:""})},className:"text-sm",children:o?"Don't have an account? Sign up":"Already have an account? Sign in"})})]})]})})]})})})},qx=({isOpen:o,onClose:h,userId:d})=>{const[c,y]=Z.useState(null),[z,p]=Z.useState([]),[R,T]=Z.useState(!1),[g,S]=Z.useState(!1),[M,U]=Z.useState("overview"),[O,H]=Z.useState(1),[X,G]=Z.useState("all"),[le,F]=Z.useState(null),[P,xe]=Z.useState(!1);Z.useEffect(()=>{o&&d&&(ee(),xe(!1),U("overview"))},[o,d]),Z.useEffect(()=>{o&&d&&M==="activity"&&(!P||O>1||X!=="all")&&ye()},[o,d,M,O,X,P]);const ee=async()=>{T(!0),F(null);try{const Y=localStorage.getItem("token");if(!Y)throw new Error("No authentication token");const Ne=await fetch(`${et}/api/users/${d}/profile`,{headers:{Authorization:`Bearer ${Y}`,"Content-Type":"application/json"}});if(!Ne.ok){const L=await Ne.json().catch(()=>({}));throw new Error(L.message||"Failed to fetch profile")}const ke=await Ne.json();y(ke.data)}catch(Y){console.error("Error fetching user profile:",Y),F(Y instanceof Error?Y.message:"Failed to load profile")}finally{T(!1)}},ye=async()=>{S(!0);try{const Y=localStorage.getItem("token");if(!Y)throw new Error("No authentication token");const Ne=new URLSearchParams({page:O.toString(),limit:"10",type:X}),ke=await fetch(`${et}/api/users/${d}/activity?${Ne}`,{headers:{Authorization:`Bearer ${Y}`,"Content-Type":"application/json"}});if(!ke.ok)throw new Error("Failed to fetch activity");const L=await ke.json();p(L.data.activities),xe(!0)}catch(Y){console.error("Error fetching user activity:",Y)}finally{S(!1)}},ge=Y=>new Date(Y).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),V=Y=>new Date(Y).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),Ce=Y=>{if(!Y)return"N/A";if(Y<60)return`${Y}m`;const Ne=Math.floor(Y/60),ke=Y%60;return`${Ne}h ${ke}m`},Be=Y=>{switch(Y){case"urgent":return"error";case"high":return"warning";case"medium":return"primary";case"low":return"secondary";default:return"secondary"}},ie=Y=>{switch(Y){case"created":return s.jsx("span",{className:"text-blue-500",children:"➕"});case"updated":return s.jsx("span",{className:"text-yellow-500",children:"✏️"});case"completed":return s.jsx("span",{className:"text-green-500",children:"✅"});case"cancelled":return s.jsx("span",{className:"text-red-500",children:"❌"});default:return s.jsx("span",{className:"text-gray-500",children:"📝"})}};return le?s.jsx(nl,{isOpen:o,onClose:h,title:"Error",size:"md",children:s.jsxs("div",{className:"text-center py-8",children:[s.jsx("div",{className:"text-red-500 text-6xl mb-4",children:"⚠️"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Unable to Load Profile"}),s.jsx("p",{className:"text-gray-600 mb-4",children:le}),s.jsx(pe,{variant:"secondary",onClick:h,children:"Close"})]})}):s.jsx(nl,{isOpen:o,onClose:h,title:"User Profile",size:"3xl",children:s.jsxs("div",{className:"space-y-6",children:[R?s.jsx("div",{className:"space-y-4",children:s.jsx("div",{className:"animate-pulse",children:s.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[s.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-xl"}),s.jsxs("div",{className:"flex-1 space-y-2",children:[s.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/3"}),s.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),s.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4"})]})]})})}):c?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[s.jsx("div",{className:"w-16 h-16 rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center",children:c.avatar_url?s.jsx("img",{src:c.avatar_url,alt:c.name,className:"w-full h-full rounded-xl object-cover"}):s.jsx("span",{className:"text-xl font-semibold text-primary-600",children:c.name.split(" ").map(Y=>Y[0]).join("")})}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-1",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:c.name}),c.current_task&&s.jsx(Pl,{status:c.current_task.status,size:"sm"})]}),s.jsx("p",{className:"text-sm text-gray-600",children:c.email}),s.jsxs("div",{className:"flex items-center space-x-4 mt-2",children:[s.jsx(ot,{variant:"secondary",size:"sm",children:c.role.charAt(0).toUpperCase()+c.role.slice(1)}),s.jsx("span",{className:"text-sm text-gray-500",children:c.department_name})]})]})]}),s.jsx("div",{className:"border-b border-gray-200",children:s.jsxs("nav",{className:"-mb-px flex space-x-8",children:[s.jsx("button",{onClick:()=>U("overview"),className:`py-2 px-1 border-b-2 font-medium text-sm ${M==="overview"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Overview"}),s.jsx("button",{onClick:()=>U("activity"),className:`py-2 px-1 border-b-2 font-medium text-sm ${M==="activity"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Task History"})]})}),M==="overview"&&s.jsxs("div",{className:"space-y-6",children:[c.current_task&&s.jsxs("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:[s.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Current Task"}),s.jsx("p",{className:"text-sm text-blue-800 mb-3",children:c.current_task.task_description}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-blue-700 font-medium",children:"Priority: "}),s.jsx(ot,{variant:Be(c.current_task.priority),size:"sm",children:c.current_task.priority})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-blue-700 font-medium",children:"Progress: "}),s.jsxs("span",{className:"text-blue-800",children:[c.current_task.progress_percentage,"%"]})]}),c.current_task.project_name&&s.jsxs("div",{className:"col-span-2",children:[s.jsx("span",{className:"text-blue-700 font-medium",children:"Project: "}),s.jsx("span",{className:"text-blue-800",children:c.current_task.project_name})]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.statistics.total_tasks}),s.jsx("div",{className:"text-sm text-gray-600",children:"Total Tasks"})]}),s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:c.statistics.tasks_last_30_days}),s.jsx("div",{className:"text-sm text-gray-600",children:"Last 30 Days"})]})]}),s.jsx("div",{className:"grid grid-cols-1 gap-6",children:s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Basic Information"}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"Email:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:c.email})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"Department:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:c.department_name})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"Role:"}),s.jsx("span",{className:"ml-2 text-gray-900 capitalize",children:c.role})]}),c.hire_date&&s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"Hire Date:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:ge(c.hire_date)})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"Timezone:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:"IST (India Standard Time)"})]}),c.last_login_at&&s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"Last Login:"}),s.jsx("span",{className:"ml-2 text-gray-900",children:V(c.last_login_at)})]})]})]})})]}),M==="activity"&&s.jsxs("div",{className:"space-y-4",children:[c.current_task&&s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),s.jsx("h4",{className:"font-medium text-green-900",children:"Current Ongoing Task"})]}),s.jsx("div",{className:"bg-white border border-green-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-1",children:s.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[s.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Active"}),s.jsx(ot,{variant:Be(c.current_task.priority),size:"sm",children:c.current_task.priority}),s.jsxs("span",{className:"text-xs text-gray-500",children:["Updated: ",V(c.current_task.updated_at)]})]}),s.jsx("p",{className:"text-sm text-gray-700 mb-2",children:c.current_task.task_description}),s.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[s.jsxs("span",{children:["Category: ",c.current_task.category.replace("-"," ")]}),s.jsxs("span",{children:["Progress: ",c.current_task.progress_percentage,"%"]}),c.current_task.project_name&&s.jsxs("span",{children:["Project: ",c.current_task.project_name]}),c.current_task.expected_finish_datetime&&s.jsxs("span",{children:["Expected Finish: ",V(c.current_task.expected_finish_datetime)]})]})]})]})})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Task History"}),s.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Filter:"}),s.jsxs("select",{value:X,onChange:Y=>{G(Y.target.value),H(1),xe(!1)},className:"text-sm border border-gray-300 rounded-md px-3 py-1",children:[s.jsx("option",{value:"all",children:"All Activities"}),s.jsx("option",{value:"created",children:"Created"}),s.jsx("option",{value:"updated",children:"Updated"}),s.jsx("option",{value:"completed",children:"Completed"}),s.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]}),g?s.jsx("div",{className:"space-y-3",children:[...Array(5)].map((Y,Ne)=>s.jsxs("div",{className:"animate-pulse bg-gray-50 rounded-lg p-4",children:[s.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),s.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]},Ne))}):z.length>0?s.jsx("div",{className:"space-y-3",children:z.map(Y=>s.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-1",children:ie(Y.action_type)}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[s.jsx("span",{className:"text-sm font-medium text-gray-900 capitalize",children:Y.action_type}),s.jsx(ot,{variant:Be(Y.priority),size:"sm",children:Y.priority}),s.jsx("span",{className:"text-xs text-gray-500",children:V(Y.created_at)})]}),s.jsx("p",{className:"text-sm text-gray-700 mb-2",children:Y.task_description}),s.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[s.jsxs("span",{children:["Category: ",Y.category.replace("-"," ")]}),Y.session_duration_minutes&&s.jsxs("span",{children:["Duration: ",Ce(Y.session_duration_minutes)]}),Y.project_name&&s.jsxs("span",{children:["Project: ",Y.project_name]})]})]})]})},Y.id))}):s.jsxs("div",{className:"text-center py-8",children:[s.jsx("div",{className:"text-gray-400 text-4xl mb-4",children:"📝"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Activity Found"}),s.jsx("p",{className:"text-gray-600",children:"No activity history matches the current filter."})]})]})]}):null,s.jsx("div",{className:"flex justify-end pt-4 border-t border-gray-200",children:s.jsx(pe,{variant:"secondary",onClick:h,children:"Close"})})]})})},Yx=({onUserSelect:o,className:h})=>{const[d,c]=Z.useState(""),[y,z]=Z.useState([]),[p,R]=Z.useState(!1),[T,g]=Z.useState(!1);Z.useEffect(()=>{const U=setTimeout(async()=>{if(d.length<2){z([]),g(!1);return}R(!0);try{const O=localStorage.getItem("token");if(!O)return;const H=new URLSearchParams({q:d,limit:"10"}),X=await fetch(`${et}/api/users/search?${H}`,{headers:{Authorization:`Bearer ${O}`,"Content-Type":"application/json"}});if(X.ok){const G=await X.json();z(G.data.users),g(!0)}}catch(O){console.error("Error searching users:",O)}finally{R(!1)}},300);return()=>clearTimeout(U)},[d]);const S=M=>{o(M),g(!1),c("")};return s.jsxs("div",{className:`relative ${h}`,children:[s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:"text",placeholder:"Search employees by name or email...",value:d,onChange:M=>c(M.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"}),p&&s.jsx("div",{className:"absolute right-3 top-2.5",children:s.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"})})]}),T&&y.length>0&&s.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:y.map(M=>s.jsx("button",{onClick:()=>S(M.id),className:"w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:outline-none focus:bg-gray-50",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center",children:M.avatar_url?s.jsx("img",{src:M.avatar_url,alt:M.name,className:"w-full h-full rounded-full object-cover"}):s.jsx("span",{className:"text-sm font-semibold text-primary-600",children:M.name.split(" ").map(U=>U[0]).join("")})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:M.name}),M.current_status&&s.jsx(Pl,{status:M.current_status,size:"sm"})]}),s.jsx("p",{className:"text-xs text-gray-500 truncate",children:M.email}),s.jsxs("p",{className:"text-xs text-gray-400",children:[M.department_name," • ",M.role]})]})]})},M.id))}),T&&y.length===0&&d.length>=2&&!p&&s.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-4 text-center",children:s.jsxs("p",{className:"text-sm text-gray-500",children:['No employees found matching "',d,'"']})})]})},Gx=({onDepartmentSelect:o,onViewProfile:h})=>{const{user:d}=Ya(),[c,y]=Z.useState([]),[z,p]=Z.useState(!0),[R,T]=Z.useState(null);return Z.useEffect(()=>{(async()=>{try{p(!0),T(null);const S=localStorage.getItem("token");if(!S){T("Authentication required");return}console.log("Fetching departments from:",`${et}/api/departments`);const M=await fetch(`${et}/api/departments`,{method:"GET",headers:{Authorization:`Bearer ${S}`,"Content-Type":"application/json"}});if(console.log("Departments response status:",M.status),!M.ok){const O=await M.text();if(console.error("Departments error response:",O),M.status===401){console.error("Authentication failed - clearing stored token"),localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.reload();return}throw new Error(`HTTP error! status: ${M.status} - ${O}`)}const U=await M.json();console.log("Departments response data:",U),U.success&&U.data&&U.data.departments?(console.log("Setting departments:",U.data.departments),y(U.data.departments)):(console.error("Invalid response format:",U),T("Invalid response from server"))}catch(S){T("Failed to load departments"),console.error("Error fetching departments:",S)}finally{p(!1)}})()},[]),s.jsxs("div",{className:"min-h-screen bg-neutral-50 pb-32",children:[s.jsx("header",{className:"bg-white border-b border-neutral-200 sticky top-0 z-30 backdrop-blur-md bg-white/95",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex items-center py-6",children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:s.jsx("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-neutral-900",children:"Employee Dashboard"}),s.jsxs("p",{className:"text-lg text-neutral-600",children:["Welcome back, ",d?.name]})]})]})})})}),s.jsx("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"text-center lg:text-left",children:[s.jsx("h2",{className:"text-3xl font-bold text-neutral-900 mb-3",children:"Departments"}),s.jsx("p",{className:"text-lg text-neutral-600 max-w-3xl",children:"Select a department to view employee status and manage team activities. Each department shows real-time employee activity and task progress."})]}),h&&s.jsx(Dt,{className:"border-indigo-100 bg-gradient-to-r from-indigo-50 to-purple-50",children:s.jsxs(Ut,{className:"p-6",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center",children:s.jsx("svg",{className:"w-5 h-5 text-indigo-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Find Employee Profile"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Search for any employee across all departments"})]})]}),s.jsx(Yx,{onUserSelect:h,className:"max-w-lg"})]})}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("h3",{className:"text-xl font-semibold text-neutral-900",children:"All Departments"}),!z&&!R&&c.length>0&&s.jsxs(ot,{variant:"secondary",size:"sm",children:[c.length," ",c.length===1?"Department":"Departments"]})]}),s.jsx("div",{className:"flex items-center space-x-3",children:s.jsxs("div",{className:"text-sm text-neutral-600",children:["Sort by: ",s.jsx("span",{className:"font-medium",children:"Activity"})]})})]}),z?s.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:[...Array(6)].map((g,S)=>s.jsxs(Dt,{className:"animate-pulse",children:[s.jsx(ci,{className:"pb-4",children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-neutral-200 rounded-xl"}),s.jsxs("div",{className:"flex-1 space-y-2",children:[s.jsx("div",{className:"h-4 bg-neutral-200 rounded w-3/4"}),s.jsx("div",{className:"h-3 bg-neutral-200 rounded w-1/2"})]})]})}),s.jsxs(Ut,{className:"pt-0 space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx("div",{className:"h-16 bg-neutral-200 rounded-lg"}),s.jsx("div",{className:"h-16 bg-neutral-200 rounded-lg"})]}),s.jsx("div",{className:"h-4 bg-neutral-200 rounded"})]})]},S))}):R?s.jsx(Dt,{className:"text-center py-12",children:s.jsxs(Ut,{children:[s.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsx("h3",{className:"text-lg font-semibold text-neutral-900 mb-2",children:"Failed to Load Departments"}),s.jsx("p",{className:"text-neutral-600 mb-4",children:R}),s.jsx(pe,{onClick:()=>window.location.reload(),children:"Try Again"})]})}):c.length===0?s.jsx(Lc,{icon:s.jsx("svg",{className:"w-12 h-12 text-neutral-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2-2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),title:"No Departments Found",description:"No departments have been set up yet. Contact your administrator to create departments and add employees.",action:{label:"Refresh",onClick:()=>window.location.reload(),variant:"secondary"}}):s.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:c.map(g=>s.jsxs(Dt,{variant:"interactive",padding:"none",onClick:()=>o(g.id),className:"group hover-lift border-l-4 border-l-indigo-500 hover:border-l-indigo-600 transition-all duration-300",children:[s.jsx(ci,{className:"pb-4",children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-indigo-100 to-purple-200 rounded-xl flex items-center justify-center group-hover:from-indigo-200 group-hover:to-purple-300 transition-all duration-300 shadow-sm",children:s.jsx("svg",{className:"w-7 h-7 text-indigo-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx(Hc,{className:"text-xl font-semibold truncate group-hover:text-indigo-700 transition-colors",children:g.name}),s.jsx(rm,{className:"text-sm mt-1 line-clamp-2 text-neutral-600",children:g.description})]})]})}),s.jsxs(Ut,{className:"pt-0 space-y-5",children:[s.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[s.jsxs("div",{className:"text-center p-3 bg-gradient-to-br from-neutral-50 to-neutral-100 rounded-lg border border-neutral-200",children:[s.jsx("div",{className:"text-xl font-bold text-neutral-900",children:g.total_employees}),s.jsx("div",{className:"text-xs text-neutral-600 font-medium",children:"Total"})]}),s.jsxs("div",{className:"text-center p-3 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg border border-green-200",children:[s.jsx("div",{className:"text-xl font-bold text-green-700",children:g.active_employees}),s.jsx("div",{className:"text-xs text-green-700 font-medium",children:"Active"})]}),s.jsxs("div",{className:"text-center p-3 bg-gradient-to-br from-amber-50 to-orange-100 rounded-lg border border-amber-200",children:[s.jsx("div",{className:"text-xl font-bold text-amber-700",children:g.idle_employees||0}),s.jsx("div",{className:"text-xs text-amber-700 font-medium",children:"Idle"})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-neutral-700 font-medium",children:"Activity Rate"}),s.jsxs(ot,{variant:g.activity_percentage>=80?"success":g.activity_percentage>=60?"warning":"error",size:"sm",children:[g.activity_percentage,"%"]})]}),s.jsx("div",{className:"w-full bg-neutral-200 rounded-full h-3 overflow-hidden shadow-inner",children:s.jsx("div",{className:`h-3 rounded-full transition-all duration-700 ease-out ${g.activity_percentage>=80?"bg-gradient-to-r from-green-500 to-emerald-600":g.activity_percentage>=60?"bg-gradient-to-r from-amber-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"}`,style:{width:`${g.activity_percentage}%`}})})]}),s.jsxs("div",{className:"flex items-center justify-center pt-3 text-sm text-indigo-600 font-semibold group-hover:text-indigo-700 transition-colors border-t border-neutral-100",children:[s.jsx("span",{children:"View Team Details"}),s.jsx("svg",{className:"ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},g.id))})]})]})})]})},Qx=({isOpen:o,onClose:h,employee:d,taskDetails:c,isLoading:y})=>{if(!d)return null;const z=S=>{if(!S)return"Not specified";try{return new Date(S).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0})}catch{return"Invalid date"}},p=S=>{if(!S)return"Not specified";try{return new Date(S).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch{return"Invalid date"}},R=S=>{switch(S){case"urgent":return"text-red-600 bg-red-100";case"high":return"text-orange-600 bg-orange-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-neutral-600 bg-neutral-100"}},T=S=>{switch(S){case"question-creation":return"Question Creation";case"project-delivery":return"Project Delivery";case"uploading":return"Uploading";case"quality-checking":return"Quality Checking";default:return S}},g=S=>{if(!S)return"Unknown";if(S<1)return"Just now";if(S<60)return`${Math.round(S)} minutes ago`;const M=Math.floor(S/60);if(M<24)return`${M} hour${M>1?"s":""} ago`;const U=Math.floor(M/24);return`${U} day${U>1?"s":""} ago`};return s.jsx(nl,{isOpen:o,onClose:h,title:"Employee Details",size:"3xl",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-neutral-50 rounded-lg",children:[s.jsx("div",{className:"w-16 h-16 rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center",children:s.jsx("span",{className:"text-xl font-semibold text-primary-600",children:d.name.split(" ").map(S=>S[0]).join("")})}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-1",children:[s.jsx("h3",{className:"text-xl font-semibold text-neutral-900",children:d.name}),d.status&&s.jsx(Pl,{status:d.status,size:"sm"})]}),s.jsx("p",{className:"text-sm text-neutral-600",children:d.email}),d.role&&s.jsx("p",{className:"text-sm text-neutral-500 capitalize",children:d.role})]})]}),y?s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{className:"animate-pulse",children:[s.jsx("div",{className:"h-4 bg-neutral-200 rounded w-1/4 mb-2"}),s.jsx("div",{className:"h-6 bg-neutral-200 rounded w-3/4 mb-4"}),s.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(6)].map((S,M)=>s.jsx("div",{className:"h-4 bg-neutral-200 rounded"},M))})]})}):c?s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-medium text-neutral-900 mb-3",children:"Current Task"}),s.jsxs("div",{className:"bg-white border border-neutral-200 rounded-lg p-6",children:[s.jsx("p",{className:"text-neutral-800 mb-6 text-base leading-relaxed",children:c.task_description}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Status:"}),s.jsxs("div",{className:"flex items-center mt-1",children:[s.jsx(Pl,{status:c.status,size:"sm"}),s.jsx("span",{className:"ml-2 text-sm capitalize",children:c.status})]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Priority:"}),s.jsx("div",{className:"mt-1",children:s.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full capitalize ${R(c.priority)}`,children:c.priority})})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Category:"}),s.jsx("p",{className:"text-sm text-neutral-800 mt-1",children:T(c.category)})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Progress:"}),s.jsx("div",{className:"mt-1",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"flex-1 bg-neutral-200 rounded-full h-2",children:s.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${c.progress_percentage}%`}})}),s.jsxs("span",{className:"text-sm font-medium text-neutral-600",children:[c.progress_percentage,"%"]})]})})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Expected Finish:"}),s.jsx("p",{className:"text-sm text-neutral-800 mt-1",children:z(c.expected_finish_datetime)})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Expected Completion:"}),s.jsx("p",{className:"text-sm text-neutral-800 mt-1",children:p(c.expected_completion_date)})]}),c.number_of_questions&&s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Number of Questions:"}),s.jsx("p",{className:"text-sm text-neutral-800 mt-1",children:c.number_of_questions})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Task ID:"}),s.jsxs("p",{className:"text-sm text-neutral-800 mt-1 font-mono",children:[c.id.substring(0,8),"..."]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-neutral-500",children:"Created:"}),s.jsx("p",{className:"text-sm text-neutral-800 mt-1",children:z(c.created_at)})]})]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.project_name&&s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-medium text-neutral-900 mb-3",children:"Project"}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("svg",{className:"w-5 h-5 text-blue-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"})}),s.jsx("span",{className:"font-medium text-blue-900",children:c.project_name})]})})]}),c.expected_finish_datetime&&s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-medium text-neutral-900 mb-3",children:"Timeline"}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("svg",{className:"w-5 h-5 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsxs("div",{children:[s.jsx("span",{className:"text-sm font-medium text-green-700",children:"Expected Finish:"}),s.jsx("p",{className:"text-green-900",children:z(c.expected_finish_datetime)})]})]})})]})]}),c.blocking_issues&&s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-medium text-neutral-900 mb-3",children:"Blocking Issues"}),s.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start",children:[s.jsx("svg",{className:"w-5 h-5 text-red-600 mr-2 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})}),s.jsx("p",{className:"text-red-800",children:c.blocking_issues})]})})]}),c.tags&&c.tags.length>0&&s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-medium text-neutral-900 mb-3",children:"Tags"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:c.tags.map(S=>s.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",style:{backgroundColor:S.color?`${S.color}20`:"#f3f4f6",color:S.color||"#374151"},children:S.name},S.id))})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-medium text-neutral-900 mb-3",children:"Last Updated"}),s.jsx("div",{className:"bg-neutral-50 border border-neutral-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-neutral-600",children:"Task updated:"}),s.jsx("p",{className:"text-neutral-800",children:z(c.updated_at)})]}),s.jsxs("div",{className:"text-right",children:[s.jsx("p",{className:"text-sm text-neutral-600",children:"Time ago:"}),s.jsx("p",{className:"text-neutral-800",children:g(d.minutes_since_update)})]})]})})]})]}):s.jsxs("div",{className:"text-center py-8",children:[s.jsx("svg",{className:"w-12 h-12 text-neutral-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),s.jsx("h3",{className:"text-lg font-medium text-neutral-900 mb-2",children:"No Current Task"}),s.jsx("p",{className:"text-neutral-600",children:"This employee doesn't have any active tasks."})]}),s.jsx("div",{className:"flex justify-end pt-4 border-t border-neutral-200",children:s.jsx(pe,{variant:"secondary",onClick:h,children:"Close"})})]})})},Xx=({departmentId:o,onBack:h,onUpdateStatus:d,onViewProfile:c})=>{const{user:y}=Ya(),[z,p]=Z.useState(""),R={"dept-1":"Engineering","dept-2":"Marketing","dept-3":"Sales","dept-4":"HR","dept-5":"Finance"},[T,g]=Z.useState([]),[S,M]=Z.useState(!0),[U,O]=Z.useState(null),[H,X]=Z.useState(null),[G,le]=Z.useState(null),[F,P]=Z.useState(!1),[xe,ee]=Z.useState(!1),[ye,ge]=Z.useState(!1),[V,Ce]=Z.useState(null);Z.useEffect(()=>{(async()=>{try{M(!0),O(null),console.log("Fetching employees for department ID:",o);const N=localStorage.getItem("token");if(!N){O("Authentication required");return}if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(o)){console.warn("Department ID is not a UUID, this suggests mock data is being used:",o),O(`Invalid department ID format. Expected UUID but got: ${o}`);return}const D=`${et}/api/departments/${o}/employees`;console.log("Making request to:",D);const ce=await fetch(D,{method:"GET",headers:{Authorization:`Bearer ${N}`,"Content-Type":"application/json"}});if(console.log("Response status:",ce.status),!ce.ok){const k=await ce.text();throw console.error("Error response:",k),new Error(`HTTP error! status: ${ce.status} - ${k}`)}const m=await ce.json();console.log("Response data:",m),m.success&&Array.isArray(m.data)?(console.log("Employee data received:",m.data),m.data.forEach((k,Q)=>{console.log(`Employee ${Q+1} status:`,k.status,typeof k.status)}),g(m.data)):(console.error("Invalid response format:",m),O("Invalid response from server"))}catch(N){O("Failed to load employees"),console.error("Error fetching employees:",N)}finally{M(!1)}})()},[o]);const Be=T.filter(L=>L.name.toLowerCase().includes(z.toLowerCase())||L.email.toLowerCase().includes(z.toLowerCase())||L.task_description&&L.task_description.toLowerCase().includes(z.toLowerCase())),ie=async L=>{X(L),P(!0),ee(!0),le(null),console.log("Attempting to fetch task details for employee:",{employeeId:L.id,employeeName:L.name,currentUser:localStorage.getItem("user"),token:localStorage.getItem("token")?"Present":"Missing"});try{const N=await Vn(L.id);console.log("Task response:",N),N&&N.success&&N.data&&le(N.data)}catch(N){console.error("Error fetching task details:",N)}finally{ee(!1)}},Y=()=>{P(!1),X(null),le(null),ee(!1)},Ne=L=>{y?.id===L.id&&(Ce({id:L.id,task_description:L.task_description,status:L.status,priority:L.priority,category:L.category,progress_percentage:L.progress_percentage,project_name:L.project_name,expected_finish_datetime:L.expected_finish_datetime,blocking_issues:L.blocking_issues,last_updated:L.last_updated}),ge(!0))},ke=async L=>{if(y?.id===L.id&&confirm("Are you sure you want to cancel your current task? This action cannot be undone."))try{const q=(await Vn())?.data?.id;if(!q){alert("No active task found to cancel.");return}const D=localStorage.getItem("token"),ce=await fetch(`${et}/api/task-updates/${q}`,{method:"DELETE",headers:{Authorization:`Bearer ${D}`,"Content-Type":"application/json"}});if(!ce.ok)throw new Error(`Failed to cancel task: ${ce.status}`);window.location.reload()}catch(N){console.error("Error canceling task:",N),alert("Failed to cancel task. Please try again.")}};return s.jsxs("div",{className:"min-h-screen bg-neutral-50 pb-32",children:[s.jsx("header",{className:"bg-white border-b border-neutral-200 sticky top-0 z-30 backdrop-blur-md bg-white/95",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex justify-between items-center py-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx(pe,{variant:"secondary",size:"sm",onClick:h,icon:s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),iconPosition:"left",children:"Back to Dashboard"}),s.jsx("div",{className:"h-6 w-px bg-neutral-300"}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-neutral-900",children:R[o]}),s.jsx("p",{className:"text-sm text-neutral-600",children:"Department Overview & Employee Status"})]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsxs(ot,{variant:"primary",size:"sm",children:[Be.length," Employees"]}),s.jsxs(ot,{variant:"success",size:"sm",children:[Be.filter(L=>L.status==="active").length," Active"]})]})]})})}),s.jsx("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[s.jsx("div",{className:"flex-1 max-w-lg",children:s.jsx(Il,{id:"search",type:"text",value:z,onChange:L=>p(L.target.value),placeholder:"Search by name, email, or task...",icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})}),s.jsx("div",{className:"flex items-center space-x-3",children:s.jsxs("div",{className:"text-sm text-neutral-600",children:["Showing ",Be.length," of ",T.length," employees"]})})]}),s.jsx("div",{className:"grid gap-6 lg:grid-cols-1",children:S?s.jsx("div",{className:"space-y-4",children:[...Array(3)].map((L,N)=>s.jsx(Dt,{className:"animate-pulse",children:s.jsx(Ut,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-neutral-200 rounded-xl"}),s.jsxs("div",{className:"flex-1 space-y-2",children:[s.jsx("div",{className:"h-4 bg-neutral-200 rounded w-1/3"}),s.jsx("div",{className:"h-3 bg-neutral-200 rounded w-1/2"}),s.jsx("div",{className:"h-3 bg-neutral-200 rounded w-3/4"})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("div",{className:"h-3 bg-neutral-200 rounded w-20"}),s.jsx("div",{className:"h-3 bg-neutral-200 rounded w-16"})]})]})})},N))}):U?s.jsx(Dt,{className:"text-center py-12",children:s.jsxs(Ut,{children:[s.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsx("h3",{className:"text-lg font-semibold text-neutral-900 mb-2",children:"Failed to Load Employees"}),s.jsx("p",{className:"text-neutral-600 mb-4",children:U}),s.jsx(pe,{onClick:()=>window.location.reload(),children:"Try Again"})]})}):T.length===0?s.jsx(Lc,{icon:s.jsx("svg",{className:"w-12 h-12 text-neutral-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),title:"No Employees Found",description:"This department doesn't have any employees yet. Contact your administrator to add employees to this department.",action:{label:"Refresh",onClick:()=>window.location.reload(),variant:"secondary"}}):Be.length===0?s.jsx(Dt,{className:"text-center py-12",children:s.jsxs(Ut,{children:[s.jsx("svg",{className:"w-12 h-12 text-neutral-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),s.jsx("h3",{className:"text-lg font-medium text-neutral-900 mb-2",children:"No employees found"}),s.jsx("p",{className:"text-neutral-600",children:"Try adjusting your search criteria."})]})}):s.jsx("div",{className:"space-y-6",children:Be.map(L=>s.jsx(Dt,{className:"hover-lift border-l-4 border-l-indigo-500 hover:border-l-indigo-600 transition-all duration-300",children:s.jsx(Ut,{className:"p-6",children:s.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[s.jsxs("div",{className:"flex items-start space-x-4 flex-1",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"w-16 h-16 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg",children:s.jsx("span",{className:"text-xl font-bold text-white",children:L.name.split(" ").map(N=>N[0]).join("").toUpperCase()})}),s.jsx("div",{className:`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white shadow-sm ${L.status==="active"?"bg-green-500":L.status==="idle"?"bg-yellow-500":"bg-gray-400"}`})]}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[s.jsx("h3",{className:"text-xl font-semibold text-neutral-900 truncate",children:L.name}),s.jsx("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${L.status==="active"?"bg-green-100 text-green-800":L.status==="idle"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:L.status?.charAt(0).toUpperCase()+L.status?.slice(1)||"Offline"})]}),s.jsxs("div",{className:"flex items-center space-x-4 mb-3",children:[s.jsx("p",{className:"text-sm text-neutral-600",children:L.email}),L.role&&s.jsx("span",{className:"text-xs px-2 py-1 bg-neutral-100 text-neutral-700 rounded-md",children:L.role})]}),s.jsx("div",{className:"bg-neutral-50 rounded-lg p-3 border border-neutral-200",children:s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("svg",{className:"w-4 h-4 mt-0.5 text-neutral-500 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-xs font-medium text-neutral-600 mb-1",children:"Current Task"}),s.jsx("p",{className:`text-sm font-medium ${qa(L.expected_finish_datetime)?"text-orange-600":"text-neutral-900"}`,children:Oc(!!L.task_description,L.task_description,L.expected_finish_datetime)}),L.expected_finish_datetime&&s.jsx("div",{className:"flex items-center mt-2 space-x-2",children:s.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${qa(L.expected_finish_datetime)?"bg-orange-100 text-orange-700":"bg-blue-100 text-blue-700"}`,children:Dc(L.expected_finish_datetime)})})]})]})})]})]}),s.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-6",children:[s.jsxs("div",{className:"text-center lg:text-right",children:[s.jsx("div",{className:"text-xs text-neutral-500 mb-1",children:"Last Updated"}),s.jsx("div",{className:"text-sm font-medium text-neutral-700",children:L.last_updated?um(L.last_updated):"Never"})]}),s.jsxs("div",{className:"flex flex-wrap gap-2 justify-center lg:justify-end",children:[s.jsx(pe,{variant:"secondary",size:"sm",onClick:()=>ie(L),icon:s.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),children:"View Details"}),c&&s.jsx(pe,{variant:"secondary",size:"sm",onClick:()=>c(L.id),icon:s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),children:"Profile"}),y?.id===L.id&&s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(pe,{variant:"primary",size:"sm",onClick:()=>d(),icon:s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),children:"Update Status"}),L.task_description&&s.jsxs(s.Fragment,{children:[s.jsx(pe,{variant:"secondary",size:"sm",onClick:()=>Ne(L),icon:s.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),children:"View Task"}),s.jsx(pe,{variant:"error",size:"sm",onClick:()=>ke(L),icon:s.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),children:"Cancel Task"})]})]})]})]})]})})},L.id))})})]})}),s.jsx(Qx,{isOpen:F,onClose:Y,employee:H,taskDetails:G,isLoading:xe}),ye&&V&&s.jsx(mm,{isOpen:ye,onClose:()=>ge(!1),taskData:V})]})},Vx=()=>{const{user:o}=Ya(),[h,d]=Z.useState({view:"dashboard"}),[c,y]=Z.useState({isOpen:!1}),[z,p]=Z.useState({isOpen:!1}),R=F=>{d({view:"department",selectedDepartmentId:F})},T=()=>{d({view:"dashboard"})},g=()=>{o&&d({...h,view:"statusUpdate",selectedEmployeeId:o.id,previousView:h.view,previousDepartmentId:h.selectedDepartmentId})},S=async F=>{try{const P=localStorage.getItem("token");if(!P){console.error("No authentication token found");return}const xe={task_description:F.task,status:F.status,priority:F.priority,category:F.taskCategory,progress_percentage:F.progressPercentage,tags:F.tags.map(ge=>ge.label)};F.relatedProject&&F.relatedProject.trim()&&(xe.project_name=F.relatedProject.trim()),F.blockingIssues&&F.blockingIssues.trim()&&(xe.blocking_issues=F.blockingIssues),F.expectedFinishDateTime&&(xe.expected_finish_datetime=F.expectedFinishDateTime),F.numberOfQuestions&&(xe.number_of_questions=F.numberOfQuestions),console.log("Submitting task update:",xe);const ee=await fetch(`${et}/api/tasks/update`,{method:"POST",headers:{Authorization:`Bearer ${P}`,"Content-Type":"application/json"},body:JSON.stringify(xe)});if(!ee.ok){const ge=await ee.json().catch(()=>({}));console.error("Task update failed:",ge),ge.errors&&Array.isArray(ge.errors)&&(console.error("Validation errors:",ge.errors),ge.errors.forEach(V=>{console.error(`Field: ${V.field}, Message: ${V.message}`)})),alert(`Task update failed: ${ge.message||"Unknown error"}`);return}const ye=await ee.json();console.log("Task updated successfully:",ye),y({isOpen:!0,taskData:F,taskId:ye.data?.id||ye.data?.task_id})}catch(P){console.error("Error updating task:",P)}},M=()=>{const F=h.previousView||"dashboard",P=h.previousDepartmentId;d({view:F,selectedDepartmentId:P,selectedEmployeeId:void 0,previousView:void 0,previousDepartmentId:void 0})},U=()=>{y({isOpen:!1});const F=h.previousView||"dashboard",P=h.previousDepartmentId;d({view:F,selectedDepartmentId:P,selectedEmployeeId:void 0,previousView:void 0,previousDepartmentId:void 0})},O=()=>{y({isOpen:!1})},H=()=>{U()},X=F=>{p({isOpen:!0,userId:F})},G=()=>{p({isOpen:!1})},le=()=>{o&&d({...h,view:"statusUpdate",selectedEmployeeId:o.id,previousView:h.view,previousDepartmentId:h.selectedDepartmentId})};return o?s.jsxs(s.Fragment,{children:[h.view==="dashboard"&&s.jsx(Gx,{onDepartmentSelect:R,onViewProfile:X}),h.view==="department"&&h.selectedDepartmentId&&s.jsx(Xx,{departmentId:h.selectedDepartmentId,onBack:T,onUpdateStatus:g,onViewProfile:X}),h.view==="statusUpdate"&&h.selectedEmployeeId&&o?.id===h.selectedEmployeeId&&s.jsx(dm,{employeeId:h.selectedEmployeeId,currentTask:"",onSubmit:S,onCancel:M}),s.jsx(Lx,{onStatusUpdate:le,onViewProfile:X}),c.isOpen&&c.taskData&&s.jsx(fm,{isOpen:c.isOpen,onClose:U,taskData:c.taskData,taskId:c.taskId,onEdit:O,onKeep:H}),z.isOpen&&z.userId&&s.jsx(qx,{isOpen:z.isOpen,onClose:G,userId:z.userId})]}):s.jsx(Bx,{})};function Zx(){return s.jsx(Dx,{children:s.jsx($h,{children:s.jsx(Vx,{})})})}Zh.createRoot(document.getElementById("root")).render(s.jsx(Z.StrictMode,{children:s.jsx(Zx,{})}));
