import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, StatusIndicator } from './ui';
import { API_BASE_URL } from '../config/api';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  department_id: string;
  department_name: string;
  role: string;
  hire_date?: string;
  timezone: string;
  avatar_url?: string;
  phone?: string;
  emergency_contact?: any;
  skills: string[];
  created_at: string;
  last_login_at?: string;
  is_active: boolean;
  statistics: {
    total_tasks: number;
    completed_tasks: number;
    completion_rate: number;
    avg_task_duration: number | null;
    tasks_last_30_days: number;
  };
  current_task?: {
    id: string;
    task_description: string;
    status: string;
    priority: string;
    category: string;
    progress_percentage: number;
    expected_finish_datetime?: string;
    updated_at: string;
    project_name?: string;
  };
  access_level: 'full' | 'limited';
}

interface Activity {
  id: string;
  task_description: string;
  status: string;
  priority: string;
  category: string;
  progress_percentage: number;
  action_type: string;
  session_duration_minutes?: number;
  created_at: string;
  project_name?: string;
  activity_type: 'success' | 'error' | 'info' | 'warning';
}

interface UserProfilePageProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
}

const UserProfilePage: React.FC<UserProfilePageProps> = ({ isOpen, onClose, userId }) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingActivities, setIsLoadingActivities] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'activity'>('overview');
  const [activityPage, setActivityPage] = useState(1);
  const [activityFilter, setActivityFilter] = useState('all');
  const [error, setError] = useState<string | null>(null);
  const [activitiesFetched, setActivitiesFetched] = useState(false);

  // Fetch user profile when modal opens and reset states
  useEffect(() => {
    if (isOpen && userId) {
      fetchUserProfile();
      setActivitiesFetched(false);
      setActiveTab('overview');
    }
  }, [isOpen, userId]);

  // Fetch activities when switching to activity tab or when filters change
  useEffect(() => {
    if (isOpen && userId && activeTab === 'activity' && (!activitiesFetched || activityPage > 1 || activityFilter !== 'all')) {
      fetchUserActivity();
    }
  }, [isOpen, userId, activeTab, activityPage, activityFilter, activitiesFetched]);

  const fetchUserProfile = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('No authentication token');

      const response = await fetch(`${API_BASE_URL}/api/users/${userId}/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch profile');
      }

      const result = await response.json();
      setProfile(result.data);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setError(error instanceof Error ? error.message : 'Failed to load profile');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserActivity = async () => {
    setIsLoadingActivities(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('No authentication token');

      const params = new URLSearchParams({
        page: activityPage.toString(),
        limit: '10',
        type: activityFilter
      });

      const response = await fetch(`${API_BASE_URL}/api/users/${userId}/activity?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch activity');
      }

      const result = await response.json();
      setActivities(result.data.activities);
      setActivitiesFetched(true);
    } catch (error) {
      console.error('Error fetching user activity:', error);
    } finally {
      setIsLoadingActivities(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (minutes?: number): string => {
    if (!minutes) return 'N/A';
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'primary';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };



  const getActivityIcon = (actionType: string) => {
    switch (actionType) {
      case 'created':
        return <span className="text-blue-500">➕</span>;
      case 'updated':
        return <span className="text-yellow-500">✏️</span>;
      case 'completed':
        return <span className="text-green-500">✅</span>;
      case 'cancelled':
        return <span className="text-red-500">❌</span>;
      default:
        return <span className="text-gray-500">📝</span>;
    }
  };

  if (error) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} title="Error" size="md">
        <div className="text-center py-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Profile</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button variant="secondary" onClick={onClose}>
            Close
          </Button>
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="User Profile" size="3xl">
      <div className="space-y-6">
        {/* Loading State */}
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-16 h-16 bg-gray-200 rounded-xl"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
              </div>
            </div>
          </div>
        ) : profile ? (
          <>
            {/* Profile Header */}
            <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
              <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                {profile.avatar_url ? (
                  <img 
                    src={profile.avatar_url} 
                    alt={profile.name}
                    className="w-full h-full rounded-xl object-cover"
                  />
                ) : (
                  <span className="text-xl font-semibold text-primary-600">
                    {profile.name.split(' ').map(n => n[0]).join('')}
                  </span>
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-1">
                  <h3 className="text-xl font-semibold text-gray-900">{profile.name}</h3>
                  {profile.current_task && (
                    <StatusIndicator status={profile.current_task.status as any} size="sm" />
                  )}
                </div>
                <p className="text-sm text-gray-600">{profile.email}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <Badge variant="secondary" size="sm">
                    {profile.role.charAt(0).toUpperCase() + profile.role.slice(1)}
                  </Badge>
                  <span className="text-sm text-gray-500">{profile.department_name}</span>
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'overview'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Overview
                </button>
                <button
                  onClick={() => setActiveTab('activity')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'activity'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Task History
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Current Task */}
                {profile.current_task && (
                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <h4 className="font-medium text-blue-900 mb-2">Current Task</h4>
                    <p className="text-sm text-blue-800 mb-3">{profile.current_task.task_description}</p>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-blue-700 font-medium">Priority: </span>
                        <Badge variant={getPriorityColor(profile.current_task.priority)} size="sm">
                          {profile.current_task.priority}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-blue-700 font-medium">Progress: </span>
                        <span className="text-blue-800">{profile.current_task.progress_percentage}%</span>
                      </div>
                      {profile.current_task.project_name && (
                        <div className="col-span-2">
                          <span className="text-blue-700 font-medium">Project: </span>
                          <span className="text-blue-800">{profile.current_task.project_name}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <div className="text-2xl font-bold text-gray-900">{profile.statistics.total_tasks}</div>
                    <div className="text-sm text-gray-600">Total Tasks</div>
                  </div>
                  <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <div className="text-2xl font-bold text-purple-600">{profile.statistics.tasks_last_30_days}</div>
                    <div className="text-sm text-gray-600">Last 30 Days</div>
                  </div>
                </div>

                {/* Profile Information */}
                <div className="grid grid-cols-1 gap-6">
                  {/* Basic Info */}
                  <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-3">Basic Information</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-gray-600">Email:</span>
                        <span className="ml-2 text-gray-900">{profile.email}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Department:</span>
                        <span className="ml-2 text-gray-900">{profile.department_name}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Role:</span>
                        <span className="ml-2 text-gray-900 capitalize">{profile.role}</span>
                      </div>
                      {profile.hire_date && (
                        <div>
                          <span className="text-gray-600">Hire Date:</span>
                          <span className="ml-2 text-gray-900">{formatDate(profile.hire_date)}</span>
                        </div>
                      )}
                      <div>
                        <span className="text-gray-600">Timezone:</span>
                        <span className="ml-2 text-gray-900">IST (India Standard Time)</span>
                      </div>
                      {profile.last_login_at && (
                        <div>
                          <span className="text-gray-600">Last Login:</span>
                          <span className="ml-2 text-gray-900">{formatDateTime(profile.last_login_at)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'activity' && (
              <div className="space-y-4">
                {/* Current Ongoing Task */}
                {profile.current_task && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <h4 className="font-medium text-green-900">Current Ongoing Task</h4>
                    </div>
                    <div className="bg-white border border-green-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-sm font-medium text-gray-900">Active</span>
                            <Badge variant={getPriorityColor(profile.current_task.priority)} size="sm">
                              {profile.current_task.priority}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              Updated: {formatDateTime(profile.current_task.updated_at)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700 mb-2">{profile.current_task.task_description}</p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span>Category: {profile.current_task.category.replace('-', ' ')}</span>
                            <span>Progress: {profile.current_task.progress_percentage}%</span>
                            {profile.current_task.project_name && (
                              <span>Project: {profile.current_task.project_name}</span>
                            )}
                            {profile.current_task.expected_finish_datetime && (
                              <span>Expected Finish: {formatDateTime(profile.current_task.expected_finish_datetime)}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Task History Section */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Task History</h4>

                  {/* Activity Filters */}
                  <div className="flex items-center space-x-4 mb-4">
                    <label className="text-sm font-medium text-gray-700">Filter:</label>
                    <select
                      value={activityFilter}
                      onChange={(e) => {
                        setActivityFilter(e.target.value);
                        setActivityPage(1);
                        setActivitiesFetched(false);
                      }}
                      className="text-sm border border-gray-300 rounded-md px-3 py-1"
                    >
                      <option value="all">All Activities</option>
                      <option value="created">Created</option>
                      <option value="updated">Updated</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                </div>

                {/* Activity List */}
                {isLoadingActivities ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="animate-pulse bg-gray-50 rounded-lg p-4">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    ))}
                  </div>
                ) : activities.length > 0 ? (
                  <div className="space-y-3">
                    {activities.map((activity) => (
                      <div key={activity.id} className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {getActivityIcon(activity.action_type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-sm font-medium text-gray-900 capitalize">
                                {activity.action_type}
                              </span>
                              <Badge variant={getPriorityColor(activity.priority)} size="sm">
                                {activity.priority}
                              </Badge>
                              <span className="text-xs text-gray-500">
                                {formatDateTime(activity.created_at)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-700 mb-2">{activity.task_description}</p>
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span>Category: {activity.category.replace('-', ' ')}</span>
                              {activity.session_duration_minutes && (
                                <span>Duration: {formatDuration(activity.session_duration_minutes)}</span>
                              )}
                              {activity.project_name && (
                                <span>Project: {activity.project_name}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 text-4xl mb-4">📝</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Activity Found</h3>
                    <p className="text-gray-600">No activity history matches the current filter.</p>
                  </div>
                )}
              </div>
            )}
          </>
        ) : null}

        {/* Close Button */}
        <div className="flex justify-end pt-4 border-t border-gray-200">
          <Button variant="secondary" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default UserProfilePage;

// User Search Component for finding profiles
interface UserSearchResult {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar_url?: string;
  department_name: string;
  current_status?: string;
}

interface UserSearchProps {
  onUserSelect: (userId: string) => void;
  className?: string;
}

export const UserSearch: React.FC<UserSearchProps> = ({ onUserSelect, className }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    const searchUsers = async () => {
      if (searchQuery.length < 2) {
        setSearchResults([]);
        setShowResults(false);
        return;
      }

      setIsSearching(true);
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const params = new URLSearchParams({
          q: searchQuery,
          limit: '10'
        });

        const response = await fetch(`${API_BASE_URL}/api/users/search?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const result = await response.json();
          setSearchResults(result.data.users);
          setShowResults(true);
        }
      } catch (error) {
        console.error('Error searching users:', error);
      } finally {
        setIsSearching(false);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const handleUserClick = (userId: string) => {
    onUserSelect(userId);
    setShowResults(false);
    setSearchQuery('');
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <input
          type="text"
          placeholder="Search employees by name or email..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
        {isSearching && (
          <div className="absolute right-3 top-2.5">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
          </div>
        )}
      </div>

      {showResults && searchResults.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {searchResults.map((user) => (
            <button
              key={user.id}
              onClick={() => handleUserClick(user.id)}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:outline-none focus:bg-gray-50"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                  {user.avatar_url ? (
                    <img
                      src={user.avatar_url}
                      alt={user.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-sm font-semibold text-primary-600">
                      {user.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 truncate">{user.name}</p>
                    {user.current_status && (
                      <StatusIndicator status={user.current_status as any} size="sm" />
                    )}
                  </div>
                  <p className="text-xs text-gray-500 truncate">{user.email}</p>
                  <p className="text-xs text-gray-400">{user.department_name} • {user.role}</p>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {showResults && searchResults.length === 0 && searchQuery.length >= 2 && !isSearching && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-4 text-center">
          <p className="text-sm text-gray-500">No employees found matching "{searchQuery}"</p>
        </div>
      )}
    </div>
  );
};
