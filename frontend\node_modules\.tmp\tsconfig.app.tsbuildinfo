{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/authform.tsx", "../../src/components/dashboard.tsx", "../../src/components/departmentview.tsx", "../../src/components/employeedetailsmodal.tsx", "../../src/components/statusupdateform.tsx", "../../src/components/taskactionmodal.tsx", "../../src/components/taskdetailsmodal.tsx", "../../src/components/userprofilepage.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/clock.tsx", "../../src/components/ui/emptystate.tsx", "../../src/components/ui/errorboundary.tsx", "../../src/components/ui/floatingheader.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/loadingspinner.tsx", "../../src/components/ui/modal.tsx", "../../src/components/ui/skeletonloader.tsx", "../../src/components/ui/statusindicator.tsx", "../../src/components/ui/index.ts", "../../src/config/api.ts", "../../src/contexts/authcontext.tsx", "../../src/utils/cn.ts", "../../src/utils/taskutils.ts", "../../src/utils/time.ts"], "version": "5.8.3"}