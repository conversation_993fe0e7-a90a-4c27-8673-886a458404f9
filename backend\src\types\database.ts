// Database types matching the PostgreSQL schema

// =====================================================
// ENUMS AND CONSTANTS
// =====================================================

export type TaskStatus = 'active' | 'idle' | 'offline';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TaskCategory =
  | 'question-creation'
  | 'project-delivery'
  | 'uploading'
  | 'quality-checking';

export type UserRole = 'employee' | 'manager' | 'admin';
export type ProjectStatus = 'active' | 'completed' | 'on_hold' | 'cancelled';
export type TaskActionType = 'created' | 'updated' | 'completed' | 'cancelled';

// =====================================================
// DATABASE ENTITY INTERFACES
// =====================================================

export interface Department {
  id: string;
  name: string;
  description?: string;
  manager_id?: string;
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
}

export interface User {
  id: string;
  email: string;
  name: string;
  password_hash: string;
  department_id: string;
  role: UserRole;
  hire_date?: Date;
  timezone: string;
  avatar_url?: string;
  phone?: string;
  emergency_contact?: Record<string, any>;
  skills?: string[];
  created_at: Date;
  updated_at: Date;
  last_login_at?: Date;
  is_active: boolean;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  client_name?: string;
  department_id?: string;
  start_date?: Date;
  end_date?: Date;
  status: ProjectStatus;
  priority: TaskPriority;
  budget?: number;
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
}

export interface TaskUpdate {
  id: string;
  user_id: string;
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  estimated_duration_minutes?: number;
  progress_percentage: number;
  project_id?: string;
  expected_completion_date?: Date;
  blocking_issues?: string;
  number_of_questions?: number;
  expected_finish_datetime?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface TaskHistory {
  id: string;
  user_id: string;
  task_update_id?: string;
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  estimated_duration_minutes?: number;
  progress_percentage?: number;
  project_id?: string;
  expected_completion_date?: Date;
  blocking_issues?: string;
  number_of_questions?: number;
  expected_finish_datetime?: Date;
  action_type: TaskActionType;
  session_duration_minutes?: number;
  created_at: Date;
}

export interface TaskTag {
  id: string;
  name: string;
  color?: string;
  description?: string;
  created_at: Date;
  usage_count: number;
}

export interface TaskUpdateTag {
  task_update_id: string;
  task_tag_id: string;
  created_at: Date;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_start: Date;
  session_end?: Date;
  ip_address?: string;
  user_agent?: string;
  device_type?: string;
  browser?: string;
  total_active_time_minutes?: number;
  tasks_completed: number;
  status_updates_count: number;
}

// =====================================================
// ANALYTICS INTERFACES
// =====================================================

export interface DailyProductivitySummary {
  id: string;
  user_id: string;
  department_id: string;
  date: Date;
  total_active_minutes: number;
  total_idle_minutes: number;
  total_offline_minutes: number;
  tasks_completed: number;
  tasks_started: number;
  status_updates_count: number;
  avg_task_completion_time_minutes?: number;
  high_priority_tasks: number;
  urgent_priority_tasks: number;
  categories_worked?: string[];
  projects_worked?: string[];
  productivity_score?: number;
  created_at: Date;
  updated_at: Date;
}

export interface DepartmentMetrics {
  id: string;
  department_id: string;
  date: Date;
  total_employees: number;
  active_employees: number;
  idle_employees: number;
  offline_employees: number;
  total_tasks_completed: number;
  avg_productivity_score?: number;
  total_active_hours?: number;
  urgent_tasks_count: number;
  blocked_tasks_count: number;
  created_at: Date;
}

export interface TaskPerformanceMetrics {
  id: string;
  category: TaskCategory;
  priority: TaskPriority;
  department_id?: string;
  date: Date;
  total_tasks: number;
  completed_tasks: number;
  avg_completion_time_minutes?: number;
  avg_estimated_vs_actual_ratio?: number;
  blocked_tasks_count: number;
  created_at: Date;
}

// =====================================================
// VIEW INTERFACES
// =====================================================

export interface CurrentEmployeeStatus {
  user_id: string;
  name: string;
  email: string;
  department_name: string;
  department_id: string;
  task_description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  category?: TaskCategory;
  progress_percentage?: number;
  project_name?: string;
  expected_completion_date?: Date;
  blocking_issues?: string;
  last_updated?: Date;
  minutes_since_update?: number;
}

export interface DepartmentSummary {
  id: string;
  name: string;
  description?: string;
  total_employees: number;
  active_employees: number;
  idle_employees: number;
  offline_employees: number;
  urgent_tasks: number;
  blocked_tasks: number;
}

// =====================================================
// CREATE/UPDATE INTERFACES
// =====================================================

export interface CreateUserData {
  email: string;
  name: string;
  password_hash: string;
  department_id: string;
  role?: UserRole;
  hire_date?: Date;
  timezone?: string;
  phone?: string;
  emergency_contact?: Record<string, any>;
  skills?: string[];
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  department_id?: string;
  role?: UserRole;
  timezone?: string;
  avatar_url?: string;
  phone?: string;
  emergency_contact?: Record<string, any>;
  skills?: string[];
  is_active?: boolean;
}

export interface CreateTaskUpdateData {
  user_id: string;
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  progress_percentage: number;
  project_name?: string; // Changed from project_id to project_name
  expected_completion_date?: Date;
  blocking_issues?: string;
  number_of_questions?: number;
  expected_finish_datetime?: Date;
}

export interface UpdateTaskUpdateData {
  task_description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  category?: TaskCategory;
  progress_percentage?: number;
  project_name?: string; // Changed from project_id to project_name
  expected_completion_date?: Date;
  blocking_issues?: string;
  number_of_questions?: number;
  expected_finish_datetime?: Date;
}

export interface CreateProjectData {
  name: string;
  description?: string;
  client_name?: string;
  department_id?: string;
  start_date?: Date;
  end_date?: Date;
  status?: ProjectStatus;
  priority?: TaskPriority;
  budget?: number;
}

// =====================================================
// QUERY RESULT INTERFACES
// =====================================================

export interface DatabaseQueryResult<T = any> {
  rows: T[];
  rowCount: number;
  command: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
