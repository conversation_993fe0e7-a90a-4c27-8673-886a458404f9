import React from 'react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import StatusIndicator from './ui/StatusIndicator';

interface TaskDetails {
  id: string;
  task_description: string;
  status: 'active' | 'idle' | 'offline';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  progress_percentage: number;
  project_name?: string; // Now using project_name instead of project_id
  expected_completion_date?: string;
  expected_finish_datetime?: string;
  blocking_issues?: string;
  number_of_questions?: number;
  tags: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  created_at: string;
  updated_at: string;
}

interface Employee {
  id: string;
  name: string;
  email: string;
  role?: string;
  status?: 'active' | 'idle' | 'offline';
  last_updated?: string;
  minutes_since_update?: number;
}

interface EmployeeDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  employee: Employee | null;
  taskDetails: TaskDetails | null;
  isLoading: boolean;
}

const EmployeeDetailsModal: React.FC<EmployeeDetailsModalProps> = ({
  isOpen,
  onClose,
  employee,
  taskDetails,
  isLoading
}) => {
  if (!employee) return null;

  // Removed formatDuration - using expected finish time instead

  const formatDateTime = (dateTimeString?: string): string => {
    if (!dateTimeString) return 'Not specified';
    try {
      const date = new Date(dateTimeString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return 'Invalid date';
    }
  };

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'Not specified';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return 'Invalid date';
    }
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-neutral-600 bg-neutral-100';
    }
  };

  const getCategoryDisplayName = (category: string): string => {
    switch (category) {
      case 'question-creation': return 'Question Creation';
      case 'project-delivery': return 'Project Delivery';
      case 'uploading': return 'Uploading';
      case 'quality-checking': return 'Quality Checking';
      default: return category;
    }
  };

  const getTimeAgo = (minutes?: number): string => {
    if (!minutes) return 'Unknown';
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${Math.round(minutes)} minutes ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    const days = Math.floor(hours / 24);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Employee Details" size="3xl">
      <div className="space-y-6">
        {/* Employee Header */}
        <div className="flex items-center space-x-4 p-4 bg-neutral-50 rounded-lg">
          <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
            <span className="text-xl font-semibold text-primary-600">
              {employee.name.split(' ').map(n => n[0]).join('')}
            </span>
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-1">
              <h3 className="text-xl font-semibold text-neutral-900">{employee.name}</h3>
              {employee.status && (
                <StatusIndicator status={employee.status} size="sm" />
              )}
            </div>
            <p className="text-sm text-neutral-600">{employee.email}</p>
            {employee.role && (
              <p className="text-sm text-neutral-500 capitalize">{employee.role}</p>
            )}
          </div>
        </div>

        {/* Task Details */}
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-4 bg-neutral-200 rounded w-1/4 mb-2"></div>
              <div className="h-6 bg-neutral-200 rounded w-3/4 mb-4"></div>
              <div className="grid grid-cols-2 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-4 bg-neutral-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ) : taskDetails ? (
          <div className="space-y-6">
            {/* Current Task */}
            <div>
              <h4 className="text-lg font-medium text-neutral-900 mb-3">Current Task</h4>
              <div className="bg-white border border-neutral-200 rounded-lg p-6">
                <p className="text-neutral-800 mb-6 text-base leading-relaxed">{taskDetails.task_description}</p>
                
                {/* Task Metadata Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <span className="text-sm font-medium text-neutral-500">Status:</span>
                    <div className="flex items-center mt-1">
                      <StatusIndicator status={taskDetails.status} size="sm" />
                      <span className="ml-2 text-sm capitalize">{taskDetails.status}</span>
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-neutral-500">Priority:</span>
                    <div className="mt-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full capitalize ${getPriorityColor(taskDetails.priority)}`}>
                        {taskDetails.priority}
                      </span>
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-neutral-500">Category:</span>
                    <p className="text-sm text-neutral-800 mt-1">{getCategoryDisplayName(taskDetails.category)}</p>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-neutral-500">Progress:</span>
                    <div className="mt-1">
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-neutral-200 rounded-full h-2">
                          <div
                            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${taskDetails.progress_percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-neutral-600">{taskDetails.progress_percentage}%</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-neutral-500">Expected Finish:</span>
                    <p className="text-sm text-neutral-800 mt-1">{formatDateTime(taskDetails.expected_finish_datetime)}</p>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-neutral-500">Expected Completion:</span>
                    <p className="text-sm text-neutral-800 mt-1">{formatDate(taskDetails.expected_completion_date)}</p>
                  </div>

                  {/* Number of Questions */}
                  {taskDetails.number_of_questions && (
                    <div>
                      <span className="text-sm font-medium text-neutral-500">Number of Questions:</span>
                      <p className="text-sm text-neutral-800 mt-1">{taskDetails.number_of_questions}</p>
                    </div>
                  )}

                  {/* Task ID */}
                  <div>
                    <span className="text-sm font-medium text-neutral-500">Task ID:</span>
                    <p className="text-sm text-neutral-800 mt-1 font-mono">{taskDetails.id.substring(0, 8)}...</p>
                  </div>

                  {/* Created Date */}
                  <div>
                    <span className="text-sm font-medium text-neutral-500">Created:</span>
                    <p className="text-sm text-neutral-800 mt-1">{formatDateTime(taskDetails.created_at)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Project Information */}
              {taskDetails.project_name && (
                <div>
                  <h4 className="text-lg font-medium text-neutral-900 mb-3">Project</h4>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
                      </svg>
                      <span className="font-medium text-blue-900">{taskDetails.project_name}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Expected Finish Time */}
              {taskDetails.expected_finish_datetime && (
                <div>
                  <h4 className="text-lg font-medium text-neutral-900 mb-3">Timeline</h4>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <span className="text-sm font-medium text-green-700">Expected Finish:</span>
                        <p className="text-green-900">{formatDateTime(taskDetails.expected_finish_datetime)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Blocking Issues */}
            {taskDetails.blocking_issues && (
              <div>
                <h4 className="text-lg font-medium text-neutral-900 mb-3">Blocking Issues</h4>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <p className="text-red-800">{taskDetails.blocking_issues}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Tags */}
            {taskDetails.tags && taskDetails.tags.length > 0 && (
              <div>
                <h4 className="text-lg font-medium text-neutral-900 mb-3">Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {taskDetails.tags.map((tag) => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                      style={{
                        backgroundColor: tag.color ? `${tag.color}20` : '#f3f4f6',
                        color: tag.color || '#374151'
                      }}
                    >
                      {tag.name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Last Updated */}
            <div>
              <h4 className="text-lg font-medium text-neutral-900 mb-3">Last Updated</h4>
              <div className="bg-neutral-50 border border-neutral-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-neutral-600">Task updated:</p>
                    <p className="text-neutral-800">{formatDateTime(taskDetails.updated_at)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-neutral-600">Time ago:</p>
                    <p className="text-neutral-800">{getTimeAgo(employee.minutes_since_update)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-neutral-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 className="text-lg font-medium text-neutral-900 mb-2">No Current Task</h3>
            <p className="text-neutral-600">This employee doesn't have any active tasks.</p>
          </div>
        )}

        {/* Close Button */}
        <div className="flex justify-end pt-4 border-t border-neutral-200">
          <Button variant="secondary" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default EmployeeDetailsModal;
